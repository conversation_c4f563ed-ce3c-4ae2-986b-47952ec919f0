{"version": 3, "file": "PipeTransport.js", "sourceRoot": "", "sources": ["../../../../src/node/PipeTransport.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EACL,gBAAgB,EAChB,UAAU,EAEV,oBAAoB,GACrB,MAAM,mBAAmB,CAAC;AAE3B;;GAEG;AACH,MAAM,OAAO,aAAa;IAUxB,YACE,SAAgC,EAChC,QAA+B;;QAXjC,2CAAkC;QAClC,gDAA0C;QAE1C,kCAAY,KAAK,EAAC;QAClB,wCAAkB,EAAE,EAAC;QASnB,uBAAA,IAAI,4BAAc,SAAS,MAAA,CAAC;QAC5B,uBAAA,IAAI,iCAAmB;YACrB,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;gBAC1C,OAAO,uBAAA,IAAI,yDAAU,MAAd,IAAI,EAAW,MAAM,CAAC,CAAC;YAChC,CAAC,CAAC;YACF,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;gBACvC,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACzB;YACH,CAAC,CAAC;YACF,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC;YAC/C,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC;SACjD,MAAA,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,OAAe;QAClB,MAAM,CAAC,CAAC,uBAAA,IAAI,+BAAU,EAAE,4BAA4B,CAAC,CAAC;QAEtD,uBAAA,IAAI,gCAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC/B,uBAAA,IAAI,gCAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IA2BD,KAAK;QACH,uBAAA,IAAI,2BAAa,IAAI,MAAA,CAAC;QACtB,oBAAoB,CAAC,uBAAA,IAAI,qCAAgB,CAAC,CAAC;IAC7C,CAAC;CACF;sRA7BW,MAAc;IACtB,MAAM,CAAC,CAAC,uBAAA,IAAI,+BAAU,EAAE,4BAA4B,CAAC,CAAC;IAEtD,IAAI,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;QACd,+HAAwB,MAAM,CAAC,QAAQ,EAAE,MAAA,CAAC;QAC1C,OAAO;KACR;IACD,MAAM,OAAO,GAAG,uBAAA,IAAI,qCAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1E,IAAI,IAAI,CAAC,SAAS,EAAE;QAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACpC;IAED,IAAI,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;IACpB,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAClC,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;SACnE;QACD,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;QAChB,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACnC;IACD,uBAAA,IAAI,iCAAmB,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,MAAA,CAAC;AAC3D,CAAC"}