{"version": 3, "file": "prev2.js", "sources": ["../../../../../../../packages/components/pagination/src/components/prev.vue"], "sourcesContent": ["<template>\n  <button\n    type=\"button\"\n    class=\"btn-prev\"\n    :disabled=\"internalDisabled\"\n    :aria-label=\"prevText || t('el.pagination.prev')\"\n    :aria-disabled=\"internalDisabled\"\n    @click=\"$emit('click', $event)\"\n  >\n    <span v-if=\"prevText\">{{ prevText }}</span>\n    <el-icon v-else>\n      <component :is=\"prevIcon\" />\n    </el-icon>\n  </button>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { paginationPrevEmits, paginationPrevProps } from './prev'\n\ndefineOptions({\n  name: 'ElPaginationPrev',\n})\n\nconst props = defineProps(paginationPrevProps)\ndefineEmits(paginationPrevEmits)\n\nconst { t } = useLocale()\n\nconst internalDisabled = computed(\n  () => props.disabled || props.currentPage <= 1\n)\n</script>\n"], "names": ["useLocale", "computed", "_openBlock", "_createElementBlock"], "mappings": ";;;;;;;;;;uCAsBc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIA,eAAU,EAAA,CAAA;AAExB,IAAA,MAAM,gBAAmB,GAAAC,YAAA,CAAA,MAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,WAAA,IAAA,CAAA,CAAA,CAAA;AAAA,IAAA,OACjB,CAAA,IAAA,EAAM,MAAY,KAAA;AAAqB,MAC/C,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;"}