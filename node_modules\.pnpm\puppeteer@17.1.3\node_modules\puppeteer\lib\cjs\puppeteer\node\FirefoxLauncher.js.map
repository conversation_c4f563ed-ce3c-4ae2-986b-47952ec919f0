{"version": 3, "file": "FirefoxLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/FirefoxLauncher.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,4CAAoB;AACpB,gDAAwB;AACxB,iDAAyC;AACzC,qDAA6C;AAE7C,2DAAmD;AACnD,yDAAiD;AAKjD,6DAA4E;AAC5E,uCAAiC;AAEjC;;GAEG;AACH,MAAa,eAAe;IAc1B,YACE,WAA+B,EAC/B,iBAAyB,EACzB,eAAwB;QAExB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,UAAsC,EAAE;QACnD,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,IAAI,GAAG,EAAE,EACT,MAAM,GAAG,KAAK,EACd,cAAc,GAAG,IAAI,EACrB,IAAI,GAAG,KAAK,EACZ,GAAG,GAAG,OAAO,CAAC,GAAG,EACjB,YAAY,GAAG,IAAI,EACnB,aAAa,GAAG,IAAI,EACpB,YAAY,GAAG,IAAI,EACnB,iBAAiB,GAAG,KAAK,EACzB,eAAe,GAAG,EAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,EAC3C,MAAM,GAAG,CAAC,EACV,OAAO,GAAG,KAAK,EACf,iBAAiB,GAAG,EAAE,EACtB,kBAAkB,GAAG,IAAI,EACzB,aAAa,GAAG,IAAI,GACrB,GAAG,OAAO,CAAC;QAEZ,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,iBAAiB,EAAE;YACtB,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;SACrD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YAC3C,gBAAgB,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CACH,CAAC;SACH;aAAM;YACL,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAChC;QAED,IACE,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAChC,OAAO,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,EACF;YACA,IAAI,IAAI,EAAE;gBACR,IAAA,kBAAM,EACJ,aAAa,KAAK,IAAI,EACtB,2EAA2E,CAC5E,CAAC;aACH;YACD,gBAAgB,CAAC,IAAI,CAAC,2BAA2B,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;SACxE;QAED,IAAI,WAA+B,CAAC;QACpC,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAE7B,gEAAgE;QAChE,gEAAgE;QAChE,MAAM,eAAe,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvD,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;YAC1B,WAAW,GAAG,gBAAgB,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;YACpD,IAAI,CAAC,WAAW,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;gBAC/C,MAAM,IAAI,KAAK,CAAC,iCAAiC,WAAW,GAAG,CAAC,CAAC;aAClE;YAED,+DAA+D;YAC/D,6BAA6B;YAC7B,iBAAiB,GAAG,KAAK,CAAC;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;YACzD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;SAC3C;aAAM;YACL,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAC3D,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACpC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;SAC9B;QACD,IAAI,iBAAiB,GAAG,cAAc,CAAC;QACvC,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,EAAC,WAAW,EAAE,cAAc,EAAC,GAAG,IAAA,0CAAqB,EAAC,IAAI,CAAC,CAAC;YAClE,IAAI,WAAW,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;aAC9B;YACD,iBAAiB,GAAG,cAAc,CAAC;SACpC;QAED,IAAI,CAAC,iBAAiB,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACpD;QAED,MAAM,MAAM,GAAG,IAAI,gCAAa,CAC9B,IAAI,CAAC,OAAO,EACZ,iBAAiB,EACjB,gBAAgB,EAChB,WAAW,EACX,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,KAAK,CAAC;YACX,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,MAAM;YACN,GAAG;YACH,IAAI;SACL,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC;QACZ,IAAI;YACF,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC;gBAC9C,OAAO,EAAE,IAAI;gBACb,OAAO;gBACP,MAAM;gBACN,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;aAC3C,CAAC,CAAC;YACH,OAAO,GAAG,MAAM,oBAAO,CAAC,OAAO,CAC7B,IAAI,CAAC,OAAO,EACZ,UAAU,EACV,EAAE,EACF,iBAAiB,EACjB,eAAe,EACf,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EACzB,OAAO,CAAC,YAAY,CACrB,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,KAAK,CAAC;SACb;QAED,IAAI,kBAAkB,EAAE;YACtB,IAAI;gBACF,MAAM,OAAO,CAAC,aAAa,CACzB,CAAC,CAAC,EAAE;oBACF,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;gBAC7B,CAAC,EACD,EAAC,OAAO,EAAC,CACV,CAAC;aACH;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,KAAK,CAAC;aACb;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,cAAc;QACZ,OAAO,IAAA,0CAAqB,EAAC,IAAI,CAAC,CAAC,cAAc,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,+DAA+D;QAC/D,IAAI,IAAI,CAAC,kBAAkB,KAAK,QAAQ,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACtB,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;aACH;YACD,MAAM,cAAc,GAAG,IAAI,kCAAc,CAAC,IAAI,CAAC,YAAY,EAAE;gBAC3D,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,cAAc,EAAE,CAAC;YAC7D,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE;gBACrB,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;aAC7C;SACF;IACH,CAAC;IAED,IAAI,OAAO;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,UAAwC,EAAE;QACpD,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,QAAQ,EACpB,IAAI,GAAG,EAAE,EACT,WAAW,GAAG,IAAI,GACnB,GAAG,OAAO,CAAC;QAEZ,MAAM,gBAAgB,GAAG,CAAC,aAAa,CAAC,CAAC;QAEzC,QAAQ,YAAE,CAAC,QAAQ,EAAE,EAAE;YACrB,KAAK,QAAQ;gBACX,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,OAAO;gBACV,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC5C,MAAM;SACT;QACD,IAAI,WAAW,EAAE;YACf,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACpC;QACD,IAAI,QAAQ,EAAE;YACZ,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACrC;QACD,IAAI,QAAQ,EAAE;YACZ,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACrC;QACD,IACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,EACF;YACA,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACtC;QACD,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC/B,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,kBAAkB,CAAC,UAAkC;QAGnD,MAAM,MAAM,GAAG,YAAY,CAAC;QAE5B,MAAM,YAAY,GAAG;YACnB,4CAA4C;YAC5C,sBAAsB,EAAE,EAAE;YAC1B,6CAA6C;YAC7C,6BAA6B,EAAE,KAAK;YACpC,0CAA0C;YAC1C,+BAA+B,EAAE,IAAI;YAErC,wDAAwD;YACxD,8BAA8B,EAAE,KAAK;YAErC,+CAA+C;YAC/C,yEAAyE;YACzE,2CAA2C,EACzC,uCAAuC;YAEzC,+DAA+D;YAC/D,UAAU;YACV,uDAAuD;YACvD,iCAAiC,EAAE,IAAI;YACvC,qBAAqB;YACrB,4DAA4D,EAAE,KAAK;YACnE,8BAA8B;YAC9B,4BAA4B,EAAE,KAAK;YACnC,iEAAiE;YACjE,oCAAoC;YACpC,2CAA2C,EAAE,IAAI;YAEjD,mCAAmC;YACnC,0CAA0C,EAAE,KAAK;YACjD,wCAAwC,EAAE,KAAK;YAC/C,sCAAsC,EAAE,KAAK;YAC7C,wCAAwC,EAAE,KAAK;YAC/C,uCAAuC,EAAE,KAAK;YAE9C,qCAAqC;YACrC,uBAAuB,EAAE,KAAK;YAC9B,sEAAsE;YACtE,wCAAwC,EAAE,KAAK;YAC/C,4CAA4C;YAC5C,mCAAmC,EAAE,KAAK;YAE1C,qBAAqB;YACrB,0BAA0B,EAAE,aAAa;YACzC,sEAAsE;YACtE,0CAA0C,EAAE,QAAQ;YACpD,sCAAsC;YACtC,sBAAsB,EAAE,CAAC;YAEzB,yEAAyE;YACzE,yEAAyE;YACzE,WAAW;YACX,6CAA6C,EAAE,KAAK;YACpD,+CAA+C;YAC/C,mCAAmC,EAAE,KAAK;YAC1C,gDAAgD;YAChD,yBAAyB,EAAE,KAAK;YAEhC,uBAAuB;YACvB,wBAAwB,EAAE,KAAK;YAC/B,uEAAuE;YACvE,uBAAuB;YACvB,iCAAiC,EAAE,KAAK;YACxC,8CAA8C;YAC9C,kCAAkC,EAAE,EAAE;YACtC,kCAAkC;YAClC,oBAAoB,EAAE,KAAK;YAE3B,6CAA6C;YAC7C,8CAA8C,EAAE,UAAU,MAAM,sBAAsB;YACtF,mDAAmD,EAAE,KAAK;YAC1D,4CAA4C,EAAE,KAAK;YACnD,6CAA6C,EAAE,KAAK;YACpD,0CAA0C,EAAE,KAAK;YAEjD,gFAAgF;YAChF,4CAA4C,EAAE,KAAK;YACnD,6DAA6D,EAAE,IAAI;YAEnE,gFAAgF;YAChF,gEAAgE;YAChE,2BAA2B,EAAE,KAAK;YAElC,wBAAwB;YACxB,8BAA8B,EAAE,KAAK;YAErC,qEAAqE;YACrE,yDAAyD;YACzD,wBAAwB,EAAE,IAAI;YAE9B,iCAAiC;YACjC,4BAA4B,EAAE,KAAK;YAEnC,gCAAgC;YAChC,gCAAgC,EAAE,CAAC;YACnC,yBAAyB,EAAE,CAAC;YAE5B,6DAA6D;YAC7D,8DAA8D;YAC9D,8BAA8B,EAAE,CAAC;YACjC,0BAA0B,EAAE,CAAC;YAE7B,4DAA4D;YAC5D,oCAAoC,EAAE,KAAK;YAE3C,6DAA6D;YAC7D,gCAAgC,EAAE,KAAK;YAEvC,iCAAiC;YACjC,iCAAiC,EAAE,IAAI;YAEvC,yDAAyD;YACzD,2BAA2B,EAAE,KAAK;YAElC,yDAAyD;YACzD,8BAA8B,EAAE,KAAK;YAErC,0DAA0D;YAC1D,mCAAmC,EAAE,UAAU,MAAM,qBAAqB;YAE1E,2EAA2E;YAC3E,yBAAyB,EAAE,KAAK;YAEhC,wDAAwD;YACxD,qCAAqC,EAAE,CAAC;YAExC,qEAAqE;YACrE,uBAAuB,EAAE,IAAI;YAC7B,4BAA4B;YAC5B,mCAAmC,EAAE,KAAK;YAC1C,qEAAqE;YACrE,mDAAmD;YACnD,sBAAsB,EAAE,IAAI;YAC5B,mBAAmB;YACnB,eAAe,EAAE,KAAK;YACtB,kBAAkB;YAClB,qBAAqB,EAAE,CAAC;YACxB,uDAAuD;YACvD,kCAAkC,EAAE,IAAI;YAExC,+DAA+D;YAC/D,iCAAiC,EAAE,KAAK;YACxC,+CAA+C;YAC/C,yEAAyE;YACzE,+BAA+B,EAAE,CAAC;YAElC,iEAAiE;YACjE,sCAAsC,EAAE,KAAK;YAE7C,wCAAwC;YACxC,mCAAmC,EAAE,KAAK;YAE1C,sEAAsE;YACtE,+BAA+B;YAC/B,yCAAyC,EAAE,CAAC;YAE5C,yDAAyD;YACzD,+BAA+B,EAAE,KAAK;YAEtC,iDAAiD;YACjD,oBAAoB,EAAE,MAAM;YAE5B,iBAAiB;YACjB,oBAAoB,EAAE,CAAC;YAEvB,oCAAoC,EAAE,KAAK;YAE3C,wDAAwD;YACxD,uDAAuD;YACvD,gBAAgB,EAAE,IAAI;YAEtB,gDAAgD;YAChD,0CAA0C,EAAE,KAAK;YACjD,4DAA4D;YAC5D,+BAA+B;YAC/B,uCAAuC,EAAE,KAAK;YAC9C,yDAAyD;YACzD,oCAAoC,EAAE,CAAC;YAEvC,kDAAkD;YAClD,0BAA0B,EAAE,UAAU,MAAM,mBAAmB;YAE/D,mEAAmE;YACnE,YAAY;YACZ,sBAAsB,EAAE,KAAK;YAC7B,qEAAqE;YACrE,uEAAuE;YACvE,wBAAwB,EAAE,KAAK;YAE/B,iCAAiC;YACjC,8BAA8B,EAAE,aAAa;YAE7C,iCAAiC;YACjC,yCAAyC,EAAE,EAAE;YAE7C,gEAAgE;YAChE,oCAAoC,EAAE,KAAK;YAE3C,4DAA4D;YAC5D,qCAAqC,EAAE,CAAC,CAAC;SAC1C,CAAC;QAEF,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,gBAAgB,CACpB,KAA6B,EAC7B,WAAmB;QAEnB,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YACvD,OAAO,aAAa,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,MAAM,YAAE,CAAC,QAAQ,CAAC,SAAS,CACzB,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,EACjC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CACjB,CAAC;QAEF,iEAAiE;QACjE,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QACrD,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YAC5B,MAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;YACrE,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;SACxD;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,UAAkC;QACrD,MAAM,oBAAoB,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,OAAO,CACpD,cAAI,CAAC,IAAI,CAAC,IAAA,gBAAM,GAAE,EAAE,gCAAgC,CAAC,CACtD,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAClD,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAEzD,OAAO,oBAAoB,CAAC;IAC9B,CAAC;CACF;AAreD,0CAqeC"}