{"version": 3, "file": "LifecycleWatcher.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/LifecycleWatcher.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAYH,OAAO,EAAC,YAAY,EAAC,MAAM,aAAa,CAAC;AACzC,OAAO,EAAC,YAAY,EAA4B,MAAM,mBAAmB,CAAC;AAC1E,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AAEjC,OAAO,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAG/C;;GAEG;AACH,oBAAY,uBAAuB,GAC/B,MAAM,GACN,kBAAkB,GAClB,cAAc,GACd,cAAc,CAAC;AAEnB;;GAEG;AACH,oBAAY,sBAAsB,GAC9B,MAAM,GACN,kBAAkB,GAClB,aAAa,GACb,mBAAmB,CAAC;AAcxB;;GAEG;AACH,qBAAa,gBAAgB;;gBAwCzB,YAAY,EAAE,YAAY,EAC1B,KAAK,EAAE,KAAK,EACZ,SAAS,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,EAC9D,OAAO,EAAE,MAAM;IAoGX,kBAAkB,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAUxD,6BAA6B,IAAI,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC;IAI3D,4BAA4B,IAAI,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC;IAI1D,gBAAgB,IAAI,OAAO,CAAC,IAAI,CAAC;IAIjC,2BAA2B,IAAI,OAAO,CAAC,KAAK,GAAG,YAAY,GAAG,SAAS,CAAC;IAyExE,OAAO,IAAI,IAAI;CAIhB"}