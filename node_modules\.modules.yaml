hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': public
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': public
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': public
  '@babel/parser@7.27.5':
    '@babel/parser': public
  '@babel/types@7.27.6':
    '@babel/types': public
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': public
  '@esbuild/aix-ppc64@0.25.2':
    '@esbuild/aix-ppc64': public
  '@esbuild/android-arm64@0.25.2':
    '@esbuild/android-arm64': public
  '@esbuild/android-arm@0.25.2':
    '@esbuild/android-arm': public
  '@esbuild/android-x64@0.25.2':
    '@esbuild/android-x64': public
  '@esbuild/darwin-arm64@0.25.2':
    '@esbuild/darwin-arm64': public
  '@esbuild/darwin-x64@0.25.2':
    '@esbuild/darwin-x64': public
  '@esbuild/freebsd-arm64@0.25.2':
    '@esbuild/freebsd-arm64': public
  '@esbuild/freebsd-x64@0.25.2':
    '@esbuild/freebsd-x64': public
  '@esbuild/linux-arm64@0.25.2':
    '@esbuild/linux-arm64': public
  '@esbuild/linux-arm@0.25.2':
    '@esbuild/linux-arm': public
  '@esbuild/linux-ia32@0.25.2':
    '@esbuild/linux-ia32': public
  '@esbuild/linux-loong64@0.25.2':
    '@esbuild/linux-loong64': public
  '@esbuild/linux-mips64el@0.25.2':
    '@esbuild/linux-mips64el': public
  '@esbuild/linux-ppc64@0.25.2':
    '@esbuild/linux-ppc64': public
  '@esbuild/linux-riscv64@0.25.2':
    '@esbuild/linux-riscv64': public
  '@esbuild/linux-s390x@0.25.2':
    '@esbuild/linux-s390x': public
  '@esbuild/linux-x64@0.25.2':
    '@esbuild/linux-x64': public
  '@esbuild/netbsd-arm64@0.25.2':
    '@esbuild/netbsd-arm64': public
  '@esbuild/netbsd-x64@0.25.2':
    '@esbuild/netbsd-x64': public
  '@esbuild/openbsd-arm64@0.25.2':
    '@esbuild/openbsd-arm64': public
  '@esbuild/openbsd-x64@0.25.2':
    '@esbuild/openbsd-x64': public
  '@esbuild/sunos-x64@0.25.2':
    '@esbuild/sunos-x64': public
  '@esbuild/win32-arm64@0.25.2':
    '@esbuild/win32-arm64': public
  '@esbuild/win32-ia32@0.25.2':
    '@esbuild/win32-ia32': public
  '@esbuild/win32-x64@0.25.2':
    '@esbuild/win32-x64': public
  '@floating-ui/core@1.6.5':
    '@floating-ui/core': public
  '@floating-ui/utils@0.2.5':
    '@floating-ui/utils': public
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': public
  '@jridgewell/gen-mapping@0.3.5':
    '@jridgewell/gen-mapping': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': public
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': public
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': public
  '@microsoft/api-extractor-model@7.30.5(@types/node@22.9.0)':
    '@microsoft/api-extractor-model': public
  '@microsoft/api-extractor@7.52.5(@types/node@22.9.0)':
    '@microsoft/api-extractor': public
  '@microsoft/tsdoc-config@0.17.1':
    '@microsoft/tsdoc-config': public
  '@microsoft/tsdoc@0.15.1':
    '@microsoft/tsdoc': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': public
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': public
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': public
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': public
  '@polka/url@1.0.0-next.25':
    '@polka/url': public
  '@rollup/rollup-android-arm-eabi@4.43.0':
    '@rollup/rollup-android-arm-eabi': public
  '@rollup/rollup-android-arm64@4.43.0':
    '@rollup/rollup-android-arm64': public
  '@rollup/rollup-darwin-arm64@4.43.0':
    '@rollup/rollup-darwin-arm64': public
  '@rollup/rollup-darwin-x64@4.43.0':
    '@rollup/rollup-darwin-x64': public
  '@rollup/rollup-freebsd-arm64@4.43.0':
    '@rollup/rollup-freebsd-arm64': public
  '@rollup/rollup-freebsd-x64@4.43.0':
    '@rollup/rollup-freebsd-x64': public
  '@rollup/rollup-linux-arm-gnueabihf@4.43.0':
    '@rollup/rollup-linux-arm-gnueabihf': public
  '@rollup/rollup-linux-arm-musleabihf@4.43.0':
    '@rollup/rollup-linux-arm-musleabihf': public
  '@rollup/rollup-linux-arm64-gnu@4.43.0':
    '@rollup/rollup-linux-arm64-gnu': public
  '@rollup/rollup-linux-arm64-musl@4.43.0':
    '@rollup/rollup-linux-arm64-musl': public
  '@rollup/rollup-linux-loongarch64-gnu@4.43.0':
    '@rollup/rollup-linux-loongarch64-gnu': public
  '@rollup/rollup-linux-powerpc64le-gnu@4.43.0':
    '@rollup/rollup-linux-powerpc64le-gnu': public
  '@rollup/rollup-linux-riscv64-gnu@4.43.0':
    '@rollup/rollup-linux-riscv64-gnu': public
  '@rollup/rollup-linux-riscv64-musl@4.43.0':
    '@rollup/rollup-linux-riscv64-musl': public
  '@rollup/rollup-linux-s390x-gnu@4.43.0':
    '@rollup/rollup-linux-s390x-gnu': public
  '@rollup/rollup-linux-x64-gnu@4.43.0':
    '@rollup/rollup-linux-x64-gnu': public
  '@rollup/rollup-linux-x64-musl@4.43.0':
    '@rollup/rollup-linux-x64-musl': public
  '@rollup/rollup-win32-arm64-msvc@4.43.0':
    '@rollup/rollup-win32-arm64-msvc': public
  '@rollup/rollup-win32-ia32-msvc@4.43.0':
    '@rollup/rollup-win32-ia32-msvc': public
  '@rollup/rollup-win32-x64-msvc@4.43.0':
    '@rollup/rollup-win32-x64-msvc': public
  '@rushstack/node-core-library@5.13.0(@types/node@22.9.0)':
    '@rushstack/node-core-library': public
  '@rushstack/rig-package@0.5.3':
    '@rushstack/rig-package': public
  '@rushstack/terminal@0.15.2(@types/node@22.9.0)':
    '@rushstack/terminal': public
  '@rushstack/ts-command-line@5.0.0(@types/node@22.9.0)':
    '@rushstack/ts-command-line': public
  '@types/argparse@1.0.38':
    '@types/argparse': public
  '@types/estree@1.0.7':
    '@types/estree': public
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': public
  '@types/web-bluetooth@0.0.15':
    '@types/web-bluetooth': public
  '@vitest/expect@2.0.5':
    '@vitest/expect': public
  '@vitest/pretty-format@2.0.5':
    '@vitest/pretty-format': public
  '@vitest/runner@2.0.5':
    '@vitest/runner': public
  '@vitest/snapshot@2.0.5':
    '@vitest/snapshot': public
  '@vitest/spy@2.0.5':
    '@vitest/spy': public
  '@vitest/utils@2.0.5':
    '@vitest/utils': public
  '@vue/compiler-core@3.5.16':
    '@vue/compiler-core': public
  '@vue/compiler-dom@3.5.16':
    '@vue/compiler-dom': public
  '@vue/compiler-sfc@3.5.16':
    '@vue/compiler-sfc': public
  '@vue/compiler-ssr@3.5.16':
    '@vue/compiler-ssr': public
  '@vue/reactivity@3.5.16':
    '@vue/reactivity': public
  '@vue/runtime-core@3.5.16':
    '@vue/runtime-core': public
  '@vue/runtime-dom@3.5.16':
    '@vue/runtime-dom': public
  '@vue/server-renderer@3.5.16(vue@3.5.16(typescript@5.5.4))':
    '@vue/server-renderer': public
  '@vue/shared@3.5.16':
    '@vue/shared': public
  '@vueuse/metadata@9.1.0':
    '@vueuse/metadata': public
  '@vueuse/shared@9.1.0(vue@3.5.16(typescript@5.5.4))':
    '@vueuse/shared': public
  abab@2.0.6:
    abab: public
  acorn-globals@6.0.0:
    acorn-globals: public
  acorn-walk@7.2.0:
    acorn-walk: public
  acorn@7.4.1:
    acorn: public
  ajv-draft-04@1.0.0(ajv@8.13.0):
    ajv-draft-04: public
  ajv-formats@3.0.1(ajv@8.13.0):
    ajv-formats: public
  ajv@8.12.0:
    ajv: public
  ansi-regex@5.0.1:
    ansi-regex: public
  ansi-styles@4.3.0:
    ansi-styles: public
  any-promise@1.3.0:
    any-promise: public
  argparse@1.0.10:
    argparse: public
  asn1@0.2.6:
    asn1: public
  assert-plus@1.0.0:
    assert-plus: public
  assertion-error@2.0.1:
    assertion-error: public
  asynckit@0.4.0:
    asynckit: public
  aws-sign2@0.7.0:
    aws-sign2: public
  aws4@1.11.0:
    aws4: public
  balanced-match@1.0.2:
    balanced-match: public
  bcrypt-pbkdf@1.0.2:
    bcrypt-pbkdf: public
  brace-expansion@1.1.11:
    brace-expansion: public
  braces@3.0.3:
    braces: public
  browser-process-hrtime@1.0.0:
    browser-process-hrtime: public
  buffer-from@1.1.2:
    buffer-from: public
  bundle-require@5.1.0(esbuild@0.25.2):
    bundle-require: public
  cac@6.7.14:
    cac: public
  caseless@0.12.0:
    caseless: public
  chai@5.1.1:
    chai: public
  check-error@2.1.1:
    check-error: public
  chokidar@4.0.3:
    chokidar: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  combined-stream@1.0.8:
    combined-stream: public
  commander@4.1.1:
    commander: public
  concat-map@0.0.1:
    concat-map: public
  confbox@0.1.8:
    confbox: public
  core-util-is@1.0.2:
    core-util-is: public
  cross-spawn@7.0.3:
    cross-spawn: public
  cssom@0.4.4:
    cssom: public
  cssstyle@2.3.0:
    cssstyle: public
  dashdash@1.14.1:
    dashdash: public
  data-urls@2.0.0:
    data-urls: public
  debug@4.4.0:
    debug: public
  decimal.js@10.3.1:
    decimal.js: public
  deep-eql@5.0.2:
    deep-eql: public
  deep-is@0.1.4:
    deep-is: public
  delayed-stream@1.0.0:
    delayed-stream: public
  domexception@2.0.1:
    domexception: public
  eastasianwidth@0.2.0:
    eastasianwidth: public
  ecc-jsbn@0.1.2:
    ecc-jsbn: public
  element-plus@2.10.1(vue@3.5.16(typescript@5.5.4)):
    element-plus: public
  emoji-regex@8.0.0:
    emoji-regex: public
  entities@4.5.0:
    entities: public
  esbuild@0.25.2:
    esbuild: public
  escodegen@1.14.3:
    escodegen: public
  esprima@4.0.1:
    esprima: public
  estraverse@4.3.0:
    estraverse: public
  estree-walker@3.0.3:
    estree-walker: public
  esutils@2.0.3:
    esutils: public
  execa@8.0.1:
    execa: public
  extend@3.0.2:
    extend: public
  extsprintf@1.3.0:
    extsprintf: public
  fast-deep-equal@3.1.3:
    fast-deep-equal: public
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: public
  fast-levenshtein@2.0.6:
    fast-levenshtein: public
  fastq@1.13.0:
    fastq: public
  fdir@6.4.6(picomatch@4.0.2):
    fdir: public
  fflate@0.8.2:
    fflate: public
  fill-range@7.1.1:
    fill-range: public
  fix-dts-default-cjs-exports@1.0.1:
    fix-dts-default-cjs-exports: public
  flatted@3.3.1:
    flatted: public
  follow-redirects@1.15.9(debug@4.3.7):
    follow-redirects: public
  foreground-child@3.3.0:
    foreground-child: public
  forever-agent@0.6.1:
    forever-agent: public
  form-data@4.0.1:
    form-data: public
  fs-extra@11.3.0:
    fs-extra: public
  fsevents@2.3.3:
    fsevents: public
  function-bind@1.1.2:
    function-bind: public
  get-func-name@2.0.2:
    get-func-name: public
  get-stream@8.0.1:
    get-stream: public
  get-tsconfig@4.10.0:
    get-tsconfig: public
  getpass@0.1.7:
    getpass: public
  glob-parent@5.1.2:
    glob-parent: public
  glob@10.4.5:
    glob: public
  graceful-fs@4.2.11:
    graceful-fs: public
  har-schema@2.0.0:
    har-schema: public
  har-validator@5.1.5:
    har-validator: public
  has-flag@4.0.0:
    has-flag: public
  hasown@2.0.2:
    hasown: public
  html-encoding-sniffer@2.0.1:
    html-encoding-sniffer: public
  http-signature@1.2.0:
    http-signature: public
  human-signals@5.0.0:
    human-signals: public
  iconv-lite@0.4.24:
    iconv-lite: public
  immutable@4.1.0:
    immutable: public
  import-lazy@4.0.0:
    import-lazy: public
  ip-regex@2.1.0:
    ip-regex: public
  is-core-module@2.14.0:
    is-core-module: public
  is-extglob@2.1.1:
    is-extglob: public
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: public
  is-glob@4.0.3:
    is-glob: public
  is-number@7.0.0:
    is-number: public
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: public
  is-stream@3.0.0:
    is-stream: public
  is-typedarray@1.0.0:
    is-typedarray: public
  isexe@2.0.0:
    isexe: public
  isstream@0.1.2:
    isstream: public
  jackspeak@3.4.3:
    jackspeak: public
  jiti@2.4.2:
    jiti: public
  jju@1.4.0:
    jju: public
  joycon@3.1.1:
    joycon: public
  jsbn@0.1.1:
    jsbn: public
  json-schema-traverse@1.0.0:
    json-schema-traverse: public
  json-schema@0.4.0:
    json-schema: public
  json-stringify-safe@5.0.1:
    json-stringify-safe: public
  jsonfile@6.1.0:
    jsonfile: public
  jsprim@1.4.2:
    jsprim: public
  levn@0.3.0:
    levn: public
  lilconfig@3.1.3:
    lilconfig: public
  lines-and-columns@1.2.4:
    lines-and-columns: public
  load-tsconfig@0.2.5:
    load-tsconfig: public
  lodash-es@4.17.21:
    lodash-es: public
  lodash.sortby@4.7.0:
    lodash.sortby: public
  loupe@3.1.1:
    loupe: public
  lru-cache@6.0.0:
    lru-cache: public
  magic-string@0.30.10:
    magic-string: public
  merge-stream@2.0.0:
    merge-stream: public
  merge2@1.4.1:
    merge2: public
  micromatch@4.0.8:
    micromatch: public
  mime-db@1.52.0:
    mime-db: public
  mime-types@2.1.35:
    mime-types: public
  mimic-fn@4.0.0:
    mimic-fn: public
  minimatch@3.0.8:
    minimatch: public
  minipass@7.1.2:
    minipass: public
  mlly@1.7.4:
    mlly: public
  mrmime@2.0.0:
    mrmime: public
  ms@2.1.2:
    ms: public
  mz@2.7.0:
    mz: public
  nanoid@3.3.11:
    nanoid: public
  npm-run-path@5.1.0:
    npm-run-path: public
  nwsapi@2.2.0:
    nwsapi: public
  oauth-sign@0.9.0:
    oauth-sign: public
  object-assign@4.1.1:
    object-assign: public
  onetime@6.0.0:
    onetime: public
  optionator@0.8.3:
    optionator: public
  package-json-from-dist@1.0.0:
    package-json-from-dist: public
  parse5@5.1.1:
    parse5: public
  path-key@4.0.0:
    path-key: public
  path-parse@1.0.7:
    path-parse: public
  path-scurry@1.11.1:
    path-scurry: public
  pathe@1.1.2:
    pathe: public
  pathval@2.0.0:
    pathval: public
  performance-now@2.1.0:
    performance-now: public
  picocolors@1.1.1:
    picocolors: public
  picomatch@4.0.2:
    picomatch: public
  pirates@4.0.7:
    pirates: public
  pkg-types@1.3.1:
    pkg-types: public
  postcss-load-config@6.0.1(jiti@2.4.2)(postcss@8.5.4)(tsx@4.19.3)(yaml@2.7.1):
    postcss-load-config: public
  postcss@8.5.4:
    postcss: public
  prelude-ls@1.1.2:
    prelude-ls: public
  proxy-from-env@1.1.0:
    proxy-from-env: public
  psl@1.8.0:
    psl: public
  punycode@2.3.1:
    punycode: public
  qs@6.5.3:
    qs: public
  queue-microtask@1.2.3:
    queue-microtask: public
  readdirp@4.0.1:
    readdirp: public
  request-promise-core@1.1.4(request@2.88.2):
    request-promise-core: public
  request-promise-native@1.0.9(request@2.88.2):
    request-promise-native: public
  request@2.88.2:
    request: public
  require-from-string@2.0.2:
    require-from-string: public
  resolve-from@5.0.0:
    resolve-from: public
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: public
  resolve@1.22.8:
    resolve: public
  reusify@1.0.4:
    reusify: public
  rollup@4.43.0:
    rollup: public
  run-parallel@1.2.0:
    run-parallel: public
  safe-buffer@5.2.1:
    safe-buffer: public
  safer-buffer@2.1.2:
    safer-buffer: public
  saxes@5.0.1:
    saxes: public
  semver@7.5.4:
    semver: public
  shebang-command@2.0.0:
    shebang-command: public
  shebang-regex@3.0.0:
    shebang-regex: public
  siginfo@2.0.0:
    siginfo: public
  signal-exit@4.1.0:
    signal-exit: public
  sirv@2.0.4:
    sirv: public
  source-map-js@1.2.1:
    source-map-js: public
  source-map-support@0.5.21:
    source-map-support: public
  source-map@0.8.0-beta.0:
    source-map: public
  sprintf-js@1.0.3:
    sprintf-js: public
  sshpk@1.17.0:
    sshpk: public
  stackback@0.0.2:
    stackback: public
  std-env@3.7.0:
    std-env: public
  stealthy-require@1.1.1:
    stealthy-require: public
  string-argv@0.3.2:
    string-argv: public
  string-width@4.2.3:
    string-width-cjs: public
  string-width@5.1.2:
    string-width: public
  strip-ansi@6.0.1:
    strip-ansi-cjs: public
  strip-ansi@7.1.0:
    strip-ansi: public
  strip-final-newline@3.0.0:
    strip-final-newline: public
  strip-json-comments@3.1.1:
    strip-json-comments: public
  sucrase@3.35.0:
    sucrase: public
  supports-color@8.1.1:
    supports-color: public
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: public
  symbol-tree@3.2.4:
    symbol-tree: public
  terser@5.36.0:
    terser: public
  thenify-all@1.6.0:
    thenify-all: public
  thenify@3.3.1:
    thenify: public
  tinybench@2.9.0:
    tinybench: public
  tinyexec@0.3.2:
    tinyexec: public
  tinyglobby@0.2.14:
    tinyglobby: public
  tinypool@1.0.1:
    tinypool: public
  tinyrainbow@1.2.0:
    tinyrainbow: public
  tinyspy@3.0.0:
    tinyspy: public
  to-regex-range@5.0.1:
    to-regex-range: public
  totalist@3.0.0:
    totalist: public
  tough-cookie@3.0.1:
    tough-cookie: public
  tr46@1.0.1:
    tr46: public
  tree-kill@1.2.2:
    tree-kill: public
  ts-interface-checker@0.1.13:
    ts-interface-checker: public
  tsup@8.5.0(@microsoft/api-extractor@7.52.5(@types/node@22.9.0))(jiti@2.4.2)(postcss@8.5.4)(tsx@4.19.3)(typescript@5.5.4)(yaml@2.7.1):
    tsup: public
  tunnel-agent@0.6.0:
    tunnel-agent: public
  tweetnacl@0.14.5:
    tweetnacl: public
  type-check@0.3.2:
    type-check: public
  ufo@1.5.4:
    ufo: public
  undici-types@6.19.8:
    undici-types: public
  universalify@2.0.1:
    universalify: public
  uri-js@4.4.1:
    uri-js: public
  uuid@3.4.0:
    uuid: public
  verror@1.10.0:
    verror: public
  vite-node@2.0.5(@types/node@22.9.0)(sass@1.79.3)(terser@5.36.0):
    vite-node: public
  vite@5.4.10(@types/node@22.9.0)(sass@1.79.3)(terser@5.36.0):
    vite: public
  vue-demi@0.14.8(vue@3.5.16(typescript@5.5.4)):
    vue-demi: public
  w3c-hr-time@1.0.2:
    w3c-hr-time: public
  w3c-xmlserializer@2.0.0:
    w3c-xmlserializer: public
  webidl-conversions@7.0.0:
    webidl-conversions: public
  whatwg-encoding@1.0.5:
    whatwg-encoding: public
  whatwg-mimetype@3.0.0:
    whatwg-mimetype: public
  whatwg-url@8.7.0:
    whatwg-url: public
  which@2.0.2:
    which: public
  why-is-node-running@2.3.0:
    why-is-node-running: public
  word-wrap@1.2.5:
    word-wrap: public
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: public
  wrap-ansi@8.1.0:
    wrap-ansi: public
  ws@7.5.8:
    ws: public
  xml-name-validator@3.0.0:
    xml-name-validator: public
  xmlchars@2.2.0:
    xmlchars: public
  yallist@4.0.0:
    yallist: public
  yaml@2.7.1:
    yaml: public
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.14.4
pendingBuilds: []
prunedAt: Tue, 29 Jul 2025 02:42:15 GMT
publicHoistPattern:
  - '*'
registries:
  '@tz': https://npm.tensorsecurity.cn/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.19.12'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.2'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm64@0.19.12'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.2'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-arm@0.19.12'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.2'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/android-x64@0.19.12'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.2'
  - '@esbuild/darwin-arm64@0.18.20'
  - '@esbuild/darwin-arm64@0.19.12'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.2'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/darwin-x64@0.19.12'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.2'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-arm64@0.19.12'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.2'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/freebsd-x64@0.19.12'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.2'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm64@0.19.12'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.2'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-arm@0.19.12'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.2'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-ia32@0.19.12'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.2'
  - '@esbuild/linux-loong64@0.14.54'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-loong64@0.19.12'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.2'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-mips64el@0.19.12'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.2'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-ppc64@0.19.12'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.2'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-riscv64@0.19.12'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.2'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-s390x@0.19.12'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.2'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/linux-x64@0.19.12'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.2'
  - '@esbuild/netbsd-arm64@0.25.2'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.19.12'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.2'
  - '@esbuild/openbsd-arm64@0.25.2'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.19.12'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.2'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/sunos-x64@0.19.12'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.2'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-arm64@0.19.12'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.2'
  - '@esbuild/win32-ia32@0.18.20'
  - '@esbuild/win32-ia32@0.19.12'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.2'
  - '@rollup/rollup-android-arm-eabi@4.24.4'
  - '@rollup/rollup-android-arm-eabi@4.43.0'
  - '@rollup/rollup-android-arm64@4.24.4'
  - '@rollup/rollup-android-arm64@4.43.0'
  - '@rollup/rollup-darwin-arm64@4.24.4'
  - '@rollup/rollup-darwin-arm64@4.43.0'
  - '@rollup/rollup-darwin-x64@4.24.4'
  - '@rollup/rollup-darwin-x64@4.43.0'
  - '@rollup/rollup-freebsd-arm64@4.24.4'
  - '@rollup/rollup-freebsd-arm64@4.43.0'
  - '@rollup/rollup-freebsd-x64@4.24.4'
  - '@rollup/rollup-freebsd-x64@4.43.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.24.4'
  - '@rollup/rollup-linux-arm-gnueabihf@4.43.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.24.4'
  - '@rollup/rollup-linux-arm-musleabihf@4.43.0'
  - '@rollup/rollup-linux-arm64-gnu@4.24.4'
  - '@rollup/rollup-linux-arm64-gnu@4.43.0'
  - '@rollup/rollup-linux-arm64-musl@4.24.4'
  - '@rollup/rollup-linux-arm64-musl@4.43.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.43.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.24.4'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.43.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.24.4'
  - '@rollup/rollup-linux-riscv64-gnu@4.43.0'
  - '@rollup/rollup-linux-riscv64-musl@4.43.0'
  - '@rollup/rollup-linux-s390x-gnu@4.24.4'
  - '@rollup/rollup-linux-s390x-gnu@4.43.0'
  - '@rollup/rollup-linux-x64-gnu@4.24.4'
  - '@rollup/rollup-linux-x64-gnu@4.43.0'
  - '@rollup/rollup-linux-x64-musl@4.24.4'
  - '@rollup/rollup-linux-x64-musl@4.43.0'
  - '@rollup/rollup-win32-arm64-msvc@4.24.4'
  - '@rollup/rollup-win32-arm64-msvc@4.43.0'
  - '@rollup/rollup-win32-ia32-msvc@4.24.4'
  - '@rollup/rollup-win32-ia32-msvc@4.43.0'
  - bindings@1.5.0
  - esbuild-android-64@0.14.47
  - esbuild-android-64@0.14.54
  - esbuild-android-arm64@0.14.47
  - esbuild-android-arm64@0.14.54
  - esbuild-darwin-64@0.14.47
  - esbuild-darwin-64@0.14.54
  - esbuild-darwin-arm64@0.14.47
  - esbuild-darwin-arm64@0.14.54
  - esbuild-freebsd-64@0.14.47
  - esbuild-freebsd-64@0.14.54
  - esbuild-freebsd-arm64@0.14.47
  - esbuild-freebsd-arm64@0.14.54
  - esbuild-linux-32@0.14.47
  - esbuild-linux-32@0.14.54
  - esbuild-linux-64@0.14.47
  - esbuild-linux-64@0.14.54
  - esbuild-linux-arm64@0.14.47
  - esbuild-linux-arm64@0.14.54
  - esbuild-linux-arm@0.14.47
  - esbuild-linux-arm@0.14.54
  - esbuild-linux-mips64le@0.14.47
  - esbuild-linux-mips64le@0.14.54
  - esbuild-linux-ppc64le@0.14.47
  - esbuild-linux-ppc64le@0.14.54
  - esbuild-linux-riscv64@0.14.47
  - esbuild-linux-riscv64@0.14.54
  - esbuild-linux-s390x@0.14.47
  - esbuild-linux-s390x@0.14.54
  - esbuild-netbsd-64@0.14.47
  - esbuild-netbsd-64@0.14.54
  - esbuild-openbsd-64@0.14.47
  - esbuild-openbsd-64@0.14.54
  - esbuild-sunos-64@0.14.47
  - esbuild-sunos-64@0.14.54
  - esbuild-windows-32@0.14.47
  - esbuild-windows-32@0.14.54
  - esbuild-windows-arm64@0.14.47
  - esbuild-windows-arm64@0.14.54
  - file-uri-to-path@1.0.0
  - fsevents@1.2.13
  - fsevents@2.3.3
  - nan@2.20.0
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\work\vue\neue-plus\node_modules\.pnpm
virtualStoreDirMaxLength: 120
