"use strict";var k=(n,o)=>()=>(o||n((o={exports:{}}).exports,o),o.exports);var $=k((Th,qr)=>{var Ze=function(n){return n&&n.Math==Math&&n};qr.exports=Ze(typeof globalThis=="object"&&globalThis)||Ze(typeof window=="object"&&window)||Ze(typeof self=="object"&&self)||Ze(typeof global=="object"&&global)||function(){return this}()||Function("return this")()});var ye=k((Ih,Mr)=>{Mr.exports=function(n){try{return!!n()}catch{return!0}}});var _e=k((Ph,Ur)=>{var $a=ye();Ur.exports=!$a(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})});var Ct=k((Nh,zr)=>{var en=ye();zr.exports=!en(function(){var n=function(){}.bind();return typeof n!="function"||n.hasOwnProperty("prototype")})});var et=k((Dh,Wr)=>{var tn=Ct(),$e=Function.prototype.call;Wr.exports=tn?$e.bind($e):function(){return $e.apply($e,arguments)}});var Hr=k(Xr=>{"use strict";var Gr={}.propertyIsEnumerable,Kr=Object.getOwnPropertyDescriptor,rn=Kr&&!Gr.call({1:2},1);Xr.f=rn?function(o){var u=Kr(this,o);return!!u&&u.enumerable}:Gr});var Et=k((Lh,Jr)=>{Jr.exports=function(n,o){return{enumerable:!(n&1),configurable:!(n&2),writable:!(n&4),value:o}}});var ce=k((Vh,Zr)=>{var Yr=Ct(),Qr=Function.prototype,sn=Qr.bind,bt=Qr.call,an=Yr&&sn.bind(bt,bt);Zr.exports=Yr?function(n){return n&&an(n)}:function(n){return n&&function(){return bt.apply(n,arguments)}}});var ti=k((Rh,ei)=>{var $r=ce(),nn=$r({}.toString),on=$r("".slice);ei.exports=function(n){return on(nn(n),8,-1)}});var ii=k((jh,ri)=>{var un=$(),hn=ce(),ln=ye(),cn=ti(),_t=un.Object,pn=hn("".split);ri.exports=ln(function(){return!_t("z").propertyIsEnumerable(0)})?function(n){return cn(n)=="String"?pn(n,""):_t(n)}:_t});var St=k((qh,si)=>{var fn=$(),dn=fn.TypeError;si.exports=function(n){if(n==null)throw dn("Can't call method on "+n);return n}});var tt=k((Mh,ai)=>{var mn=ii(),vn=St();ai.exports=function(n){return mn(vn(n))}});var pe=k((Uh,ni)=>{ni.exports=function(n){return typeof n=="function"}});var Ie=k((zh,oi)=>{var gn=pe();oi.exports=function(n){return typeof n=="object"?n!==null:gn(n)}});var rt=k((Wh,ui)=>{var wt=$(),xn=pe(),yn=function(n){return xn(n)?n:void 0};ui.exports=function(n,o){return arguments.length<2?yn(wt[n]):wt[n]&&wt[n][o]}});var li=k((Gh,hi)=>{var An=ce();hi.exports=An({}.isPrototypeOf)});var pi=k((Kh,ci)=>{var Cn=rt();ci.exports=Cn("navigator","userAgent")||""});var yi=k((Xh,xi)=>{var gi=$(),kt=pi(),fi=gi.process,di=gi.Deno,mi=fi&&fi.versions||di&&di.version,vi=mi&&mi.v8,he,it;vi&&(he=vi.split("."),it=he[0]>0&&he[0]<4?1:+(he[0]+he[1]));!it&&kt&&(he=kt.match(/Edge\/(\d+)/),(!he||he[1]>=74)&&(he=kt.match(/Chrome\/(\d+)/),he&&(it=+he[1])));xi.exports=it});var Ft=k((Hh,Ci)=>{var Ai=yi(),En=ye();Ci.exports=!!Object.getOwnPropertySymbols&&!En(function(){var n=Symbol();return!String(n)||!(Object(n)instanceof Symbol)||!Symbol.sham&&Ai&&Ai<41})});var Bt=k((Jh,Ei)=>{var bn=Ft();Ei.exports=bn&&!Symbol.sham&&typeof Symbol.iterator=="symbol"});var Tt=k((Yh,bi)=>{var _n=$(),Sn=rt(),wn=pe(),kn=li(),Fn=Bt(),Bn=_n.Object;bi.exports=Fn?function(n){return typeof n=="symbol"}:function(n){var o=Sn("Symbol");return wn(o)&&kn(o.prototype,Bn(n))}});var Si=k((Qh,_i)=>{var Tn=$(),In=Tn.String;_i.exports=function(n){try{return In(n)}catch{return"Object"}}});var ki=k((Zh,wi)=>{var Pn=$(),Nn=pe(),Dn=Si(),On=Pn.TypeError;wi.exports=function(n){if(Nn(n))return n;throw On(Dn(n)+" is not a function")}});var Bi=k(($h,Fi)=>{var Ln=ki();Fi.exports=function(n,o){var u=n[o];return u==null?void 0:Ln(u)}});var Ii=k((el,Ti)=>{var Vn=$(),It=et(),Pt=pe(),Nt=Ie(),Rn=Vn.TypeError;Ti.exports=function(n,o){var u,l;if(o==="string"&&Pt(u=n.toString)&&!Nt(l=It(u,n))||Pt(u=n.valueOf)&&!Nt(l=It(u,n))||o!=="string"&&Pt(u=n.toString)&&!Nt(l=It(u,n)))return l;throw Rn("Can't convert object to primitive value")}});var Ni=k((tl,Pi)=>{Pi.exports=!1});var st=k((rl,Oi)=>{var Di=$(),jn=Object.defineProperty;Oi.exports=function(n,o){try{jn(Di,n,{value:o,configurable:!0,writable:!0})}catch{Di[n]=o}return o}});var at=k((il,Vi)=>{var qn=$(),Mn=st(),Li="__core-js_shared__",Un=qn[Li]||Mn(Li,{});Vi.exports=Un});var Dt=k((sl,ji)=>{var zn=Ni(),Ri=at();(ji.exports=function(n,o){return Ri[n]||(Ri[n]=o!==void 0?o:{})})("versions",[]).push({version:"3.22.2",mode:zn?"pure":"global",copyright:"\xA9 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.22.2/LICENSE",source:"https://github.com/zloirock/core-js"})});var Mi=k((al,qi)=>{var Wn=$(),Gn=St(),Kn=Wn.Object;qi.exports=function(n){return Kn(Gn(n))}});var Ae=k((nl,Ui)=>{var Xn=ce(),Hn=Mi(),Jn=Xn({}.hasOwnProperty);Ui.exports=Object.hasOwn||function(o,u){return Jn(Hn(o),u)}});var Ot=k((ol,zi)=>{var Yn=ce(),Qn=0,Zn=Math.random(),$n=Yn(1 .toString);zi.exports=function(n){return"Symbol("+(n===void 0?"":n)+")_"+$n(++Qn+Zn,36)}});var Ji=k((ul,Hi)=>{var eo=$(),to=Dt(),Wi=Ae(),ro=Ot(),Gi=Ft(),Xi=Bt(),Pe=to("wks"),Se=eo.Symbol,Ki=Se&&Se.for,io=Xi?Se:Se&&Se.withoutSetter||ro;Hi.exports=function(n){if(!Wi(Pe,n)||!(Gi||typeof Pe[n]=="string")){var o="Symbol."+n;Gi&&Wi(Se,n)?Pe[n]=Se[n]:Xi&&Ki?Pe[n]=Ki(o):Pe[n]=io(o)}return Pe[n]}});var $i=k((hl,Zi)=>{var so=$(),ao=et(),Yi=Ie(),Qi=Tt(),no=Bi(),oo=Ii(),uo=Ji(),ho=so.TypeError,lo=uo("toPrimitive");Zi.exports=function(n,o){if(!Yi(n)||Qi(n))return n;var u=no(n,lo),l;if(u){if(o===void 0&&(o="default"),l=ao(u,n,o),!Yi(l)||Qi(l))return l;throw ho("Can't convert object to primitive value")}return o===void 0&&(o="number"),oo(n,o)}});var Lt=k((ll,es)=>{var co=$i(),po=Tt();es.exports=function(n){var o=co(n,"string");return po(o)?o:o+""}});var is=k((cl,rs)=>{var fo=$(),ts=Ie(),Vt=fo.document,mo=ts(Vt)&&ts(Vt.createElement);rs.exports=function(n){return mo?Vt.createElement(n):{}}});var Rt=k((pl,ss)=>{var vo=_e(),go=ye(),xo=is();ss.exports=!vo&&!go(function(){return Object.defineProperty(xo("div"),"a",{get:function(){return 7}}).a!=7})});var jt=k(ns=>{var yo=_e(),Ao=et(),Co=Hr(),Eo=Et(),bo=tt(),_o=Lt(),So=Ae(),wo=Rt(),as=Object.getOwnPropertyDescriptor;ns.f=yo?as:function(o,u){if(o=bo(o),u=_o(u),wo)try{return as(o,u)}catch{}if(So(o,u))return Eo(!Ao(Co.f,o,u),o[u])}});var us=k((dl,os)=>{var ko=_e(),Fo=ye();os.exports=ko&&Fo(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!=42})});var qt=k((ml,ls)=>{var hs=$(),Bo=Ie(),To=hs.String,Io=hs.TypeError;ls.exports=function(n){if(Bo(n))return n;throw Io(To(n)+" is not an object")}});var Gt=k(ps=>{var Po=$(),No=_e(),Do=Rt(),Oo=us(),nt=qt(),cs=Lt(),Lo=Po.TypeError,Mt=Object.defineProperty,Vo=Object.getOwnPropertyDescriptor,Ut="enumerable",zt="configurable",Wt="writable";ps.f=No?Oo?function(o,u,l){if(nt(o),u=cs(u),nt(l),typeof o=="function"&&u==="prototype"&&"value"in l&&Wt in l&&!l[Wt]){var m=Vo(o,u);m&&m[Wt]&&(o[u]=l.value,l={configurable:zt in l?l[zt]:m[zt],enumerable:Ut in l?l[Ut]:m[Ut],writable:!1})}return Mt(o,u,l)}:Mt:function(o,u,l){if(nt(o),u=cs(u),nt(l),Do)try{return Mt(o,u,l)}catch{}if("get"in l||"set"in l)throw Lo("Accessors not supported");return"value"in l&&(o[u]=l.value),o}});var ot=k((gl,fs)=>{var Ro=_e(),jo=Gt(),qo=Et();fs.exports=Ro?function(n,o,u){return jo.f(n,o,qo(1,u))}:function(n,o,u){return n[o]=u,n}});var Xt=k((xl,ds)=>{var Mo=ce(),Uo=pe(),Kt=at(),zo=Mo(Function.toString);Uo(Kt.inspectSource)||(Kt.inspectSource=function(n){return zo(n)});ds.exports=Kt.inspectSource});var gs=k((yl,vs)=>{var Wo=$(),Go=pe(),Ko=Xt(),ms=Wo.WeakMap;vs.exports=Go(ms)&&/native code/.test(Ko(ms))});var As=k((Al,ys)=>{var Xo=Dt(),Ho=Ot(),xs=Xo("keys");ys.exports=function(n){return xs[n]||(xs[n]=Ho(n))}});var Ht=k((Cl,Cs)=>{Cs.exports={}});var ks=k((El,ws)=>{var Jo=gs(),Ss=$(),Jt=ce(),Yo=Ie(),Qo=ot(),Yt=Ae(),Qt=at(),Zo=As(),$o=Ht(),Es="Object already initialized",$t=Ss.TypeError,eu=Ss.WeakMap,ut,qe,ht,tu=function(n){return ht(n)?qe(n):ut(n,{})},ru=function(n){return function(o){var u;if(!Yo(o)||(u=qe(o)).type!==n)throw $t("Incompatible receiver, "+n+" required");return u}};Jo||Qt.state?(Ce=Qt.state||(Qt.state=new eu),bs=Jt(Ce.get),Zt=Jt(Ce.has),_s=Jt(Ce.set),ut=function(n,o){if(Zt(Ce,n))throw new $t(Es);return o.facade=n,_s(Ce,n,o),o},qe=function(n){return bs(Ce,n)||{}},ht=function(n){return Zt(Ce,n)}):(we=Zo("state"),$o[we]=!0,ut=function(n,o){if(Yt(n,we))throw new $t(Es);return o.facade=n,Qo(n,we,o),o},qe=function(n){return Yt(n,we)?n[we]:{}},ht=function(n){return Yt(n,we)});var Ce,bs,Zt,_s,we;ws.exports={set:ut,get:qe,has:ht,enforce:tu,getterFor:ru}});var Ts=k((bl,Bs)=>{var er=_e(),iu=Ae(),Fs=Function.prototype,su=er&&Object.getOwnPropertyDescriptor,tr=iu(Fs,"name"),au=tr&&function(){}.name==="something",nu=tr&&(!er||er&&su(Fs,"name").configurable);Bs.exports={EXISTS:tr,PROPER:au,CONFIGURABLE:nu}});var Os=k((_l,Ds)=>{var ou=$(),Is=pe(),uu=Ae(),Ps=ot(),hu=st(),lu=Xt(),Ns=ks(),cu=Ts().CONFIGURABLE,pu=Ns.get,fu=Ns.enforce,du=String(String).split("String");(Ds.exports=function(n,o,u,l){var m=l?!!l.unsafe:!1,E=l?!!l.enumerable:!1,y=l?!!l.noTargetGet:!1,A=l&&l.name!==void 0?l.name:o,b;if(Is(u)&&(String(A).slice(0,7)==="Symbol("&&(A="["+String(A).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!uu(u,"name")||cu&&u.name!==A)&&Ps(u,"name",A),b=fu(u),b.source||(b.source=du.join(typeof A=="string"?A:""))),n===ou){E?n[o]=u:hu(o,u);return}else m?!y&&n[o]&&(E=!0):delete n[o];E?n[o]=u:Ps(n,o,u)})(Function.prototype,"toString",function(){return Is(this)&&pu(this).source||lu(this)})});var rr=k((Sl,Ls)=>{var mu=Math.ceil,vu=Math.floor;Ls.exports=function(n){var o=+n;return o!==o||o===0?0:(o>0?vu:mu)(o)}});var Rs=k((wl,Vs)=>{var gu=rr(),xu=Math.max,yu=Math.min;Vs.exports=function(n,o){var u=gu(n);return u<0?xu(u+o,0):yu(u,o)}});var qs=k((kl,js)=>{var Au=rr(),Cu=Math.min;js.exports=function(n){return n>0?Cu(Au(n),9007199254740991):0}});var Us=k((Fl,Ms)=>{var Eu=qs();Ms.exports=function(n){return Eu(n.length)}});var Gs=k((Bl,Ws)=>{var bu=tt(),_u=Rs(),Su=Us(),zs=function(n){return function(o,u,l){var m=bu(o),E=Su(m),y=_u(l,E),A;if(n&&u!=u){for(;E>y;)if(A=m[y++],A!=A)return!0}else for(;E>y;y++)if((n||y in m)&&m[y]===u)return n||y||0;return!n&&-1}};Ws.exports={includes:zs(!0),indexOf:zs(!1)}});var Hs=k((Tl,Xs)=>{var wu=ce(),ir=Ae(),ku=tt(),Fu=Gs().indexOf,Bu=Ht(),Ks=wu([].push);Xs.exports=function(n,o){var u=ku(n),l=0,m=[],E;for(E in u)!ir(Bu,E)&&ir(u,E)&&Ks(m,E);for(;o.length>l;)ir(u,E=o[l++])&&(~Fu(m,E)||Ks(m,E));return m}});var Ys=k((Il,Js)=>{Js.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]});var Zs=k(Qs=>{var Tu=Hs(),Iu=Ys(),Pu=Iu.concat("length","prototype");Qs.f=Object.getOwnPropertyNames||function(o){return Tu(o,Pu)}});var ea=k($s=>{$s.f=Object.getOwnPropertySymbols});var ra=k((Dl,ta)=>{var Nu=rt(),Du=ce(),Ou=Zs(),Lu=ea(),Vu=qt(),Ru=Du([].concat);ta.exports=Nu("Reflect","ownKeys")||function(o){var u=Ou.f(Vu(o)),l=Lu.f;return l?Ru(u,l(o)):u}});var aa=k((Ol,sa)=>{var ia=Ae(),ju=ra(),qu=jt(),Mu=Gt();sa.exports=function(n,o,u){for(var l=ju(o),m=Mu.f,E=qu.f,y=0;y<l.length;y++){var A=l[y];!ia(n,A)&&!(u&&ia(u,A))&&m(n,A,E(o,A))}}});var oa=k((Ll,na)=>{var Uu=ye(),zu=pe(),Wu=/#|\.prototype\./,Me=function(n,o){var u=Ku[Gu(n)];return u==Hu?!0:u==Xu?!1:zu(o)?Uu(o):!!o},Gu=Me.normalize=function(n){return String(n).replace(Wu,".").toLowerCase()},Ku=Me.data={},Xu=Me.NATIVE="N",Hu=Me.POLYFILL="P";na.exports=Me});var ha=k((Vl,ua)=>{var sr=$(),Ju=jt().f,Yu=ot(),Qu=Os(),Zu=st(),$u=aa(),eh=oa();ua.exports=function(n,o){var u=n.target,l=n.global,m=n.stat,E,y,A,b,g,V;if(l?y=sr:m?y=sr[u]||Zu(u,{}):y=(sr[u]||{}).prototype,y)for(A in o){if(g=o[A],n.noTargetGet?(V=Ju(y,A),b=V&&V.value):b=y[A],E=eh(l?A:u+(m?".":"#")+A,n.forced),!E&&b!==void 0){if(typeof g==typeof b)continue;$u(g,b)}(n.sham||b&&b.sham)&&Yu(g,"sham",!0),Qu(y,A,g,n)}}});var la=k(()=>{var th=ha(),rh=$();th({global:!0},{globalThis:rh})});la();var ur=Object.defineProperty,ih=Object.getOwnPropertyDescriptor,hr=Object.getOwnPropertyNames,sh=Object.prototype.hasOwnProperty,ca=(n,o)=>function(){return n&&(o=(0,n[hr(n)[0]])(n=0)),o},Y=(n,o)=>function(){return o||(0,n[hr(n)[0]])((o={exports:{}}).exports,o),o.exports},ah=(n,o)=>{for(var u in o)ur(n,u,{get:o[u],enumerable:!0})},nh=(n,o,u,l)=>{if(o&&typeof o=="object"||typeof o=="function")for(let m of hr(o))!sh.call(n,m)&&m!==u&&ur(n,m,{get:()=>o[m],enumerable:!(l=ih(o,m))||l.enumerable});return n},oh=n=>nh(ur({},"__esModule",{value:!0}),n),J=ca({"<define:process>"(){}}),lr=Y({"src/common/parser-create-error.js"(n,o){"use strict";J();function u(l,m){let E=new SyntaxError(l+" ("+m.start.line+":"+m.start.column+")");return E.loc=m,E}o.exports=u}}),pa=Y({"src/utils/try-combinations.js"(n,o){"use strict";J();function u(){let l;for(var m=arguments.length,E=new Array(m),y=0;y<m;y++)E[y]=arguments[y];for(let[A,b]of E.entries())try{return{result:b()}}catch(g){A===0&&(l=g)}return{error:l}}o.exports=u}}),fa={};ah(fa,{EOL:()=>or,arch:()=>uh,cpus:()=>Aa,default:()=>Sa,endianness:()=>da,freemem:()=>xa,getNetworkInterfaces:()=>_a,hostname:()=>ma,loadavg:()=>va,networkInterfaces:()=>ba,platform:()=>hh,release:()=>Ea,tmpDir:()=>ar,tmpdir:()=>nr,totalmem:()=>ya,type:()=>Ca,uptime:()=>ga});function da(){if(typeof lt>"u"){var n=new ArrayBuffer(2),o=new Uint8Array(n),u=new Uint16Array(n);if(o[0]=1,o[1]=2,u[0]===258)lt="BE";else if(u[0]===513)lt="LE";else throw new Error("unable to figure out endianess")}return lt}function ma(){return typeof globalThis.location<"u"?globalThis.location.hostname:""}function va(){return[]}function ga(){return 0}function xa(){return Number.MAX_VALUE}function ya(){return Number.MAX_VALUE}function Aa(){return[]}function Ca(){return"Browser"}function Ea(){return typeof globalThis.navigator<"u"?globalThis.navigator.appVersion:""}function ba(){}function _a(){}function uh(){return"javascript"}function hh(){return"browser"}function ar(){return"/tmp"}var lt,nr,or,Sa,lh=ca({"node-modules-polyfills:os"(){J(),nr=ar,or=`
`,Sa={EOL:or,tmpdir:nr,tmpDir:ar,networkInterfaces:ba,getNetworkInterfaces:_a,release:Ea,type:Ca,cpus:Aa,totalmem:ya,freemem:xa,uptime:ga,loadavg:va,hostname:ma,endianness:da}}}),ch=Y({"node-modules-polyfills-commonjs:os"(n,o){J();var u=(lh(),oh(fa));if(u&&u.default){o.exports=u.default;for(let l in u)o.exports[l]=u[l]}else u&&(o.exports=u)}}),ph=Y({"node_modules/detect-newline/index.js"(n,o){"use strict";J();var u=l=>{if(typeof l!="string")throw new TypeError("Expected a string");let m=l.match(/(?:\r?\n)/g)||[];if(m.length===0)return;let E=m.filter(A=>A===`\r
`).length,y=m.length-E;return E>y?`\r
`:`
`};o.exports=u,o.exports.graceful=l=>typeof l=="string"&&u(l)||`
`}}),fh=Y({"node_modules/jest-docblock/build/index.js"(n){"use strict";J(),Object.defineProperty(n,"__esModule",{value:!0}),n.extract=T,n.parse=q,n.parseWithComments=B,n.print=I,n.strip=v;function o(){let C=ch();return o=function(){return C},C}function u(){let C=l(ph());return u=function(){return C},C}function l(C){return C&&C.__esModule?C:{default:C}}var m=/\*\/$/,E=/^\/\*\*/,y=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,A=/(^|\s+)\/\/([^\r\n]*)/g,b=/^(\r?\n)+/,g=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,V=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,R=/(\r?\n|^) *\* ?/g,N=[];function T(C){let H=C.match(y);return H?H[0].trimLeft():""}function v(C){let H=C.match(y);return H&&H[0]?C.substring(H[0].length):C}function q(C){return B(C).pragmas}function B(C){let H=(0,u().default)(C)||o().EOL;C=C.replace(E,"").replace(m,"").replace(R,"$1");let L="";for(;L!==C;)L=C,C=C.replace(g,"".concat(H,"$1 $2").concat(H));C=C.replace(b,"").trimRight();let i=Object.create(null),_=C.replace(V,"").replace(b,"").trimRight(),P;for(;P=V.exec(C);){let M=P[2].replace(A,"");typeof i[P[1]]=="string"||Array.isArray(i[P[1]])?i[P[1]]=N.concat(i[P[1]],M):i[P[1]]=M}return{comments:_,pragmas:i}}function I(C){let{comments:H="",pragmas:L={}}=C,i=(0,u().default)(H)||o().EOL,_="/**",P=" *",M=" */",ee=Object.keys(L),ae=ee.map(ie=>w(ie,L[ie])).reduce((ie,Ne)=>ie.concat(Ne),[]).map(ie=>P+" "+ie+i).join("");if(!H){if(ee.length===0)return"";if(ee.length===1&&!Array.isArray(L[ee[0]])){let ie=L[ee[0]];return"".concat(_," ").concat(w(ee[0],ie)[0]).concat(M)}}let te=H.split(i).map(ie=>"".concat(P," ").concat(ie)).join(i)+i;return _+i+(H?te:"")+(H&&ee.length?P+i:"")+ae+M}function w(C,H){return N.concat(H).map(L=>"@".concat(C," ").concat(L).trim())}}}),dh=Y({"src/common/end-of-line.js"(n,o){"use strict";J();function u(y){let A=y.indexOf("\r");return A>=0?y.charAt(A+1)===`
`?"crlf":"cr":"lf"}function l(y){switch(y){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}function m(y,A){let b;switch(A){case`
`:b=/\n/g;break;case"\r":b=/\r/g;break;case`\r
`:b=/\r\n/g;break;default:throw new Error('Unexpected "eol" '.concat(JSON.stringify(A),"."))}let g=y.match(b);return g?g.length:0}function E(y){return y.replace(/\r\n?/g,`
`)}o.exports={guessEndOfLine:u,convertEndOfLineToChars:l,countEndOfLineChars:m,normalizeEndOfLine:E}}}),mh=Y({"src/language-js/utils/get-shebang.js"(n,o){"use strict";J();function u(l){if(!l.startsWith("#!"))return"";let m=l.indexOf(`
`);return m===-1?l:l.slice(0,m)}o.exports=u}}),vh=Y({"src/language-js/pragma.js"(n,o){"use strict";J();var{parseWithComments:u,strip:l,extract:m,print:E}=fh(),{normalizeEndOfLine:y}=dh(),A=mh();function b(R){let N=A(R);N&&(R=R.slice(N.length+1));let T=m(R),{pragmas:v,comments:q}=u(T);return{shebang:N,text:R,pragmas:v,comments:q}}function g(R){let N=Object.keys(b(R).pragmas);return N.includes("prettier")||N.includes("format")}function V(R){let{shebang:N,text:T,pragmas:v,comments:q}=b(R),B=l(T),I=E({pragmas:Object.assign({format:""},v),comments:q.trimStart()});return(N?"".concat(N,`
`):"")+y(I)+(B.startsWith(`
`)?`
`:`

`)+B}o.exports={hasPragma:g,insertPragma:V}}}),gh=Y({"src/utils/is-non-empty-array.js"(n,o){"use strict";J();function u(l){return Array.isArray(l)&&l.length>0}o.exports=u}}),wa=Y({"src/language-js/loc.js"(n,o){"use strict";J();var u=gh();function l(b,g){let{ignoreDecorators:V}=g||{};if(!V){let R=b.declaration&&b.declaration.decorators||b.decorators;if(u(R))return l(R[0])}return b.range?b.range[0]:b.start}function m(b){return b.range?b.range[1]:b.end}function E(b,g){let V=l(b);return Number.isInteger(V)&&V===l(g)}function y(b,g){let V=m(b);return Number.isInteger(V)&&V===m(g)}function A(b,g){return E(b,g)&&y(b,g)}o.exports={locStart:l,locEnd:m,hasSameLocStart:E,hasSameLoc:A}}}),ka=Y({"src/language-js/parse/utils/create-parser.js"(n,o){"use strict";J();var{hasPragma:u}=vh(),{locStart:l,locEnd:m}=wa();function E(y){return y=typeof y=="function"?{parse:y}:y,Object.assign({astFormat:"estree",hasPragma:u,locStart:l,locEnd:m},y)}o.exports=E}}),xh=Y({"src/language-js/utils/is-ts-keyword-type.js"(n,o){"use strict";J();function u(l){let{type:m}=l;return m.startsWith("TS")&&m.endsWith("Keyword")}o.exports=u}}),yh=Y({"src/language-js/utils/is-block-comment.js"(n,o){"use strict";J();var u=new Set(["Block","CommentBlock","MultiLine"]),l=m=>u.has(m==null?void 0:m.type);o.exports=l}}),Ah=Y({"src/language-js/utils/is-type-cast-comment.js"(n,o){"use strict";J();var u=yh();function l(m){return u(m)&&m.value[0]==="*"&&/@type\b/.test(m.value)}o.exports=l}}),Ch=Y({"src/utils/get-last.js"(n,o){"use strict";J();var u=l=>l[l.length-1];o.exports=u}}),Fa=Y({"src/language-js/parse/postprocess/visit-node.js"(n,o){"use strict";J();function u(l,m){if(Array.isArray(l)){for(let E=0;E<l.length;E++)l[E]=u(l[E],m);return l}if(l&&typeof l=="object"&&typeof l.type=="string"){let E=Object.keys(l);for(let y=0;y<E.length;y++)l[E[y]]=u(l[E[y]],m);return m(l)||l}return l}o.exports=u}}),Ba=Y({"src/language-js/parse/postprocess/throw-syntax-error.js"(n,o){"use strict";J();var u=lr();function l(m,E){let{start:y,end:A}=m.loc;throw u(E,{start:{line:y.line,column:y.column+1},end:{line:A.line,column:A.column+1}})}o.exports=l}}),Eh=Y({"src/language-js/parse/postprocess/typescript.js"(n,o){"use strict";J();var u=Fa(),l=Ba();function m(A,b,g){let V=A.decorators;if(!Array.isArray(V))return;let R=b.decorators;(!Array.isArray(R)||R.length!==V.length||V.some(N=>{let T=g.get(N);return!T||!R.includes(T)}))&&l(b,"Leading decorators must be attached to a class declaration")}function E(A,b){A.kind!==167||A.modifiers&&!A.modifiers.some(R=>R.kind===126)||A.initializer&&b.value===null&&l(b,"Abstract property cannot have an initializer")}function y(A,b){let{esTreeNodeToTSNodeMap:g,tsNodeToESTreeNodeMap:V}=b.tsParseResult;u(A,R=>{let N=g.get(R);if(!N)return;let T=V.get(N);T===R&&(m(N,T,V),E(N,T))})}o.exports={throwErrorForInvalidNodes:y}}}),Ta=Y({"src/language-js/parse/postprocess/index.js"(n,o){"use strict";J();var{locStart:u,locEnd:l}=wa(),m=xh(),E=Ah(),y=Ch(),A=Fa(),{throwErrorForInvalidNodes:b}=Eh(),g=Ba();function V(v,q){if(q.parser==="typescript"&&/@|abstract/.test(q.originalText)&&b(v,q),q.parser!=="typescript"&&q.parser!=="flow"&&q.parser!=="acorn"&&q.parser!=="espree"&&q.parser!=="meriyah"){let I=new Set;v=A(v,w=>{w.leadingComments&&w.leadingComments.some(E)&&I.add(u(w))}),v=A(v,w=>{if(w.type==="ParenthesizedExpression"){let{expression:C}=w;if(C.type==="TypeCastExpression")return C.range=w.range,C;let H=u(w);if(!I.has(H))return C.extra=Object.assign(Object.assign({},C.extra),{},{parenthesized:!0}),C}})}return v=A(v,I=>{switch(I.type){case"ChainExpression":return R(I.expression);case"LogicalExpression":{if(N(I))return T(I);break}case"VariableDeclaration":{let w=y(I.declarations);w&&w.init&&B(I,w);break}case"TSParenthesizedType":return m(I.typeAnnotation)||I.typeAnnotation.type==="TSThisType"||(I.typeAnnotation.range=[u(I),l(I)]),I.typeAnnotation;case"TSTypeParameter":if(typeof I.name=="string"){let w=u(I);I.name={type:"Identifier",name:I.name,range:[w,w+I.name.length]}}break;case"ObjectExpression":if(q.parser==="typescript"){let w=I.properties.find(C=>C.type==="Property"&&C.value.type==="TSEmptyBodyFunctionExpression");w&&g(w.value,"Unexpected token.")}break;case"SequenceExpression":{let w=y(I.expressions);I.range=[u(I),Math.min(l(w),l(I))];break}case"TopicReference":q.__isUsingHackPipeline=!0;break;case"ExportAllDeclaration":{let{exported:w}=I;if(q.parser==="meriyah"&&w&&w.type==="Identifier"){let C=q.originalText.slice(u(w),l(w));(C.startsWith('"')||C.startsWith("'"))&&(I.exported=Object.assign(Object.assign({},I.exported),{},{type:"Literal",value:I.exported.name,raw:C}))}break}}}),v;function B(I,w){q.originalText[l(w)]!==";"&&(I.range=[u(I),l(w)])}}function R(v){switch(v.type){case"CallExpression":v.type="OptionalCallExpression",v.callee=R(v.callee);break;case"MemberExpression":v.type="OptionalMemberExpression",v.object=R(v.object);break;case"TSNonNullExpression":v.expression=R(v.expression);break}return v}function N(v){return v.type==="LogicalExpression"&&v.right.type==="LogicalExpression"&&v.operator===v.right.operator}function T(v){return N(v)?T({type:"LogicalExpression",operator:v.operator,left:T({type:"LogicalExpression",operator:v.operator,left:v.left,right:v.right.left,range:[u(v.left),l(v.right.left)]}),right:v.right.right,range:[u(v),l(v)]}):v}o.exports=V}}),ct=Y({"node_modules/acorn/dist/acorn.js"(n,o){J(),function(u,l){typeof n=="object"&&typeof o<"u"?l(n):typeof define=="function"&&define.amd?define(["exports"],l):(u=typeof globalThis<"u"?globalThis:u||self,l(u.acorn={}))}(n,function(u){"use strict";var l={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},m="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",E={5:m,"5module":m+" export import",6:m+" const class extends export import super"},y=/^in(stanceof)?$/,A="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",b="\u200C\u200D\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0898-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECD\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F",g=new RegExp("["+A+"]"),V=new RegExp("["+A+b+"]");A=b=null;var R=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2637,96,16,1070,4050,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,46,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,482,44,11,6,17,0,322,29,19,43,1269,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4152,8,221,3,5761,15,7472,3104,541,1507,4938],N=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,357,0,62,13,1495,6,110,6,6,9,4759,9,787719,239];function T(e,t){for(var r=65536,s=0;s<t.length;s+=2){if(r+=t[s],r>e)return!1;if(r+=t[s+1],r>=e)return!0}}function v(e,t){return e<65?e===36:e<91?!0:e<97?e===95:e<123?!0:e<=65535?e>=170&&g.test(String.fromCharCode(e)):t===!1?!1:T(e,R)}function q(e,t){return e<48?e===36:e<58?!0:e<65?!1:e<91?!0:e<97?e===95:e<123?!0:e<=65535?e>=170&&V.test(String.fromCharCode(e)):t===!1?!1:T(e,R)||T(e,N)}var B=function(t,r){r===void 0&&(r={}),this.label=t,this.keyword=r.keyword,this.beforeExpr=!!r.beforeExpr,this.startsExpr=!!r.startsExpr,this.isLoop=!!r.isLoop,this.isAssign=!!r.isAssign,this.prefix=!!r.prefix,this.postfix=!!r.postfix,this.binop=r.binop||null,this.updateContext=null};function I(e,t){return new B(e,{beforeExpr:!0,binop:t})}var w={beforeExpr:!0},C={startsExpr:!0},H={};function L(e,t){return t===void 0&&(t={}),t.keyword=e,H[e]=new B(e,t)}var i={num:new B("num",C),regexp:new B("regexp",C),string:new B("string",C),name:new B("name",C),privateId:new B("privateId",C),eof:new B("eof"),bracketL:new B("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new B("]"),braceL:new B("{",{beforeExpr:!0,startsExpr:!0}),braceR:new B("}"),parenL:new B("(",{beforeExpr:!0,startsExpr:!0}),parenR:new B(")"),comma:new B(",",w),semi:new B(";",w),colon:new B(":",w),dot:new B("."),question:new B("?",w),questionDot:new B("?."),arrow:new B("=>",w),template:new B("template"),invalidTemplate:new B("invalidTemplate"),ellipsis:new B("...",w),backQuote:new B("`",C),dollarBraceL:new B("${",{beforeExpr:!0,startsExpr:!0}),eq:new B("=",{beforeExpr:!0,isAssign:!0}),assign:new B("_=",{beforeExpr:!0,isAssign:!0}),incDec:new B("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new B("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:I("||",1),logicalAND:I("&&",2),bitwiseOR:I("|",3),bitwiseXOR:I("^",4),bitwiseAND:I("&",5),equality:I("==/!=/===/!==",6),relational:I("</>/<=/>=",7),bitShift:I("<</>>/>>>",8),plusMin:new B("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:I("%",10),star:I("*",10),slash:I("/",10),starstar:new B("**",{beforeExpr:!0}),coalesce:I("??",1),_break:L("break"),_case:L("case",w),_catch:L("catch"),_continue:L("continue"),_debugger:L("debugger"),_default:L("default",w),_do:L("do",{isLoop:!0,beforeExpr:!0}),_else:L("else",w),_finally:L("finally"),_for:L("for",{isLoop:!0}),_function:L("function",C),_if:L("if"),_return:L("return",w),_switch:L("switch"),_throw:L("throw",w),_try:L("try"),_var:L("var"),_const:L("const"),_while:L("while",{isLoop:!0}),_with:L("with"),_new:L("new",{beforeExpr:!0,startsExpr:!0}),_this:L("this",C),_super:L("super",C),_class:L("class",C),_extends:L("extends",w),_export:L("export"),_import:L("import",C),_null:L("null",C),_true:L("true",C),_false:L("false",C),_in:L("in",{beforeExpr:!0,binop:7}),_instanceof:L("instanceof",{beforeExpr:!0,binop:7}),_typeof:L("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:L("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:L("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},_=/\r\n?|\n|\u2028|\u2029/,P=new RegExp(_.source,"g");function M(e){return e===10||e===13||e===8232||e===8233}function ee(e,t,r){r===void 0&&(r=e.length);for(var s=t;s<r;s++){var a=e.charCodeAt(s);if(M(a))return s<r-1&&a===13&&e.charCodeAt(s+1)===10?s+2:s+1}return-1}var ae=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,te=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,ie=Object.prototype,Ne=ie.hasOwnProperty,p=ie.toString,D=Object.hasOwn||function(e,t){return Ne.call(e,t)},S=Array.isArray||function(e){return p.call(e)==="[object Array]"};function f(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}var F=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,K=function(t,r){this.line=t,this.column=r};K.prototype.offset=function(t){return new K(this.line,this.column+t)};var X=function(t,r,s){this.start=r,this.end=s,t.sourceFile!==null&&(this.source=t.sourceFile)};function oe(e,t){for(var r=1,s=0;;){var a=ee(e,s,t);if(a<0)return new K(r,t-s);++r,s=a}}var me={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:null,allowSuperOutsideMethod:null,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},ve=!1;function pt(e){var t={};for(var r in me)t[r]=e&&D(e,r)?e[r]:me[r];if(t.ecmaVersion==="latest"?t.ecmaVersion=1e8:t.ecmaVersion==null?(!ve&&typeof console=="object"&&console.warn&&(ve=!0,console.warn(`Since Acorn 8.0.0, options.ecmaVersion is required.
Defaulting to 2020, but this will stop working in the future.`)),t.ecmaVersion=11):t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),t.allowReserved==null&&(t.allowReserved=t.ecmaVersion<5),S(t.onToken)){var s=t.onToken;t.onToken=function(a){return s.push(a)}}return S(t.onComment)&&(t.onComment=ft(t,t.onComment)),t}function ft(e,t){return function(r,s,a,h,c,d){var x={type:r?"Block":"Line",value:s,start:a,end:h};e.locations&&(x.loc=new X(this,c,d)),e.ranges&&(x.range=[a,h]),t.push(x)}}var Ee=1,ge=2,De=4,Ue=8,cr=16,pr=32,dt=64,fr=128,Oe=256,mt=Ee|ge|Oe;function vt(e,t){return ge|(e?De:0)|(t?Ue:0)}var ze=0,gt=1,fe=2,dr=3,mr=4,vr=5,Z=function(t,r,s){this.options=t=pt(t),this.sourceFile=t.sourceFile,this.keywords=f(E[t.ecmaVersion>=6?6:t.sourceType==="module"?"5module":5]);var a="";t.allowReserved!==!0&&(a=l[t.ecmaVersion>=6?6:t.ecmaVersion===5?5:3],t.sourceType==="module"&&(a+=" await")),this.reservedWords=f(a);var h=(a?a+" ":"")+l.strict;this.reservedWordsStrict=f(h),this.reservedWordsStrictBind=f(h+" "+l.strictBind),this.input=String(r),this.containsEsc=!1,s?(this.pos=s,this.lineStart=this.input.lastIndexOf(`
`,s-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(_).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=i.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule=t.sourceType==="module",this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.potentialArrowInForAwait=!1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports=Object.create(null),this.pos===0&&t.allowHashBang&&this.input.slice(0,2)==="#!"&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(Ee),this.regexpState=null,this.privateNameStack=[]},le={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},canAwait:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0},allowNewDotTarget:{configurable:!0},inClassStaticBlock:{configurable:!0}};Z.prototype.parse=function(){var t=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(t)},le.inFunction.get=function(){return(this.currentVarScope().flags&ge)>0},le.inGenerator.get=function(){return(this.currentVarScope().flags&Ue)>0&&!this.currentVarScope().inClassFieldInit},le.inAsync.get=function(){return(this.currentVarScope().flags&De)>0&&!this.currentVarScope().inClassFieldInit},le.canAwait.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e];if(t.inClassFieldInit||t.flags&Oe)return!1;if(t.flags&ge)return(t.flags&De)>0}return this.inModule&&this.options.ecmaVersion>=13||this.options.allowAwaitOutsideFunction},le.allowSuper.get=function(){var e=this.currentThisScope(),t=e.flags,r=e.inClassFieldInit;return(t&dt)>0||r||this.options.allowSuperOutsideMethod},le.allowDirectSuper.get=function(){return(this.currentThisScope().flags&fr)>0},le.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},le.allowNewDotTarget.get=function(){var e=this.currentThisScope(),t=e.flags,r=e.inClassFieldInit;return(t&(ge|Oe))>0||r},le.inClassStaticBlock.get=function(){return(this.currentVarScope().flags&Oe)>0},Z.extend=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];for(var s=this,a=0;a<t.length;a++)s=t[a](s);return s},Z.parse=function(t,r){return new this(r,t).parse()},Z.parseExpressionAt=function(t,r,s){var a=new this(s,t,r);return a.nextToken(),a.parseExpression()},Z.tokenizer=function(t,r){return new this(r,t)},Object.defineProperties(Z.prototype,le);var se=Z.prototype,Pa=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;se.strictDirective=function(e){for(;;){te.lastIndex=e,e+=te.exec(this.input)[0].length;var t=Pa.exec(this.input.slice(e));if(!t)return!1;if((t[1]||t[2])==="use strict"){te.lastIndex=e+t[0].length;var r=te.exec(this.input),s=r.index+r[0].length,a=this.input.charAt(s);return a===";"||a==="}"||_.test(r[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(a)||a==="!"&&this.input.charAt(s+1)==="=")}e+=t[0].length,te.lastIndex=e,e+=te.exec(this.input)[0].length,this.input[e]===";"&&e++}},se.eat=function(e){return this.type===e?(this.next(),!0):!1},se.isContextual=function(e){return this.type===i.name&&this.value===e&&!this.containsEsc},se.eatContextual=function(e){return this.isContextual(e)?(this.next(),!0):!1},se.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},se.canInsertSemicolon=function(){return this.type===i.eof||this.type===i.braceR||_.test(this.input.slice(this.lastTokEnd,this.start))},se.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},se.semicolon=function(){!this.eat(i.semi)&&!this.insertSemicolon()&&this.unexpected()},se.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},se.expect=function(e){this.eat(e)||this.unexpected()},se.unexpected=function(e){this.raise(e!=null?e:this.start,"Unexpected token")};function We(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1}se.checkPatternErrors=function(e,t){if(!!e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var r=t?e.parenthesizedAssign:e.parenthesizedBind;r>-1&&this.raiseRecoverable(r,"Parenthesized pattern")}},se.checkExpressionErrors=function(e,t){if(!e)return!1;var r=e.shorthandAssign,s=e.doubleProto;if(!t)return r>=0||s>=0;r>=0&&this.raise(r,"Shorthand property assignments are valid only in destructuring patterns"),s>=0&&this.raiseRecoverable(s,"Redefinition of __proto__ property")},se.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},se.isSimpleAssignTarget=function(e){return e.type==="ParenthesizedExpression"?this.isSimpleAssignTarget(e.expression):e.type==="Identifier"||e.type==="MemberExpression"};var j=Z.prototype;j.parseTopLevel=function(e){var t=Object.create(null);for(e.body||(e.body=[]);this.type!==i.eof;){var r=this.parseStatement(null,!0,t);e.body.push(r)}if(this.inModule)for(var s=0,a=Object.keys(this.undefinedExports);s<a.length;s+=1){var h=a[s];this.raiseRecoverable(this.undefinedExports[h].start,"Export '"+h+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var xt={kind:"loop"},Na={kind:"switch"};j.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;te.lastIndex=this.pos;var t=te.exec(this.input),r=this.pos+t[0].length,s=this.input.charCodeAt(r);if(s===91||s===92||s>55295&&s<56320)return!0;if(e)return!1;if(s===123)return!0;if(v(s,!0)){for(var a=r+1;q(s=this.input.charCodeAt(a),!0);)++a;if(s===92||s>55295&&s<56320)return!0;var h=this.input.slice(r,a);if(!y.test(h))return!0}return!1},j.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;te.lastIndex=this.pos;var e=te.exec(this.input),t=this.pos+e[0].length,r;return!_.test(this.input.slice(this.pos,t))&&this.input.slice(t,t+8)==="function"&&(t+8===this.input.length||!(q(r=this.input.charCodeAt(t+8))||r>55295&&r<56320))},j.parseStatement=function(e,t,r){var s=this.type,a=this.startNode(),h;switch(this.isLet(e)&&(s=i._var,h="let"),s){case i._break:case i._continue:return this.parseBreakContinueStatement(a,s.keyword);case i._debugger:return this.parseDebuggerStatement(a);case i._do:return this.parseDoStatement(a);case i._for:return this.parseForStatement(a);case i._function:return e&&(this.strict||e!=="if"&&e!=="label")&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(a,!1,!e);case i._class:return e&&this.unexpected(),this.parseClass(a,!0);case i._if:return this.parseIfStatement(a);case i._return:return this.parseReturnStatement(a);case i._switch:return this.parseSwitchStatement(a);case i._throw:return this.parseThrowStatement(a);case i._try:return this.parseTryStatement(a);case i._const:case i._var:return h=h||this.value,e&&h!=="var"&&this.unexpected(),this.parseVarStatement(a,h);case i._while:return this.parseWhileStatement(a);case i._with:return this.parseWithStatement(a);case i.braceL:return this.parseBlock(!0,a);case i.semi:return this.parseEmptyStatement(a);case i._export:case i._import:if(this.options.ecmaVersion>10&&s===i._import){te.lastIndex=this.pos;var c=te.exec(this.input),d=this.pos+c[0].length,x=this.input.charCodeAt(d);if(x===40||x===46)return this.parseExpressionStatement(a,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),s===i._import?this.parseImport(a):this.parseExport(a,r);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(a,!0,!e);var U=this.value,W=this.parseExpression();return s===i.name&&W.type==="Identifier"&&this.eat(i.colon)?this.parseLabeledStatement(a,U,W,e):this.parseExpressionStatement(a,W)}},j.parseBreakContinueStatement=function(e,t){var r=t==="break";this.next(),this.eat(i.semi)||this.insertSemicolon()?e.label=null:this.type!==i.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var s=0;s<this.labels.length;++s){var a=this.labels[s];if((e.label==null||a.name===e.label.name)&&(a.kind!=null&&(r||a.kind==="loop")||e.label&&r))break}return s===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,r?"BreakStatement":"ContinueStatement")},j.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},j.parseDoStatement=function(e){return this.next(),this.labels.push(xt),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(i._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(i.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},j.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&this.canAwait&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(xt),this.enterScope(0),this.expect(i.parenL),this.type===i.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var r=this.isLet();if(this.type===i._var||this.type===i._const||r){var s=this.startNode(),a=r?"let":this.value;return this.next(),this.parseVar(s,!0,a),this.finishNode(s,"VariableDeclaration"),(this.type===i._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&s.declarations.length===1?(this.options.ecmaVersion>=9&&(this.type===i._in?t>-1&&this.unexpected(t):e.await=t>-1),this.parseForIn(e,s)):(t>-1&&this.unexpected(t),this.parseFor(e,s))}var h=this.isContextual("let"),c=!1,d=new We,x=this.parseExpression(t>-1?"await":!0,d);return this.type===i._in||(c=this.options.ecmaVersion>=6&&this.isContextual("of"))?(this.options.ecmaVersion>=9&&(this.type===i._in?t>-1&&this.unexpected(t):e.await=t>-1),h&&c&&this.raise(x.start,"The left-hand side of a for-of loop may not start with 'let'."),this.toAssignable(x,!1,d),this.checkLValPattern(x),this.parseForIn(e,x)):(this.checkExpressionErrors(d,!0),t>-1&&this.unexpected(t),this.parseFor(e,x))},j.parseFunctionStatement=function(e,t,r){return this.next(),this.parseFunction(e,Le|(r?0:yt),!1,t)},j.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(i._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},j.parseReturnStatement=function(e){return!this.inFunction&&!this.options.allowReturnOutsideFunction&&this.raise(this.start,"'return' outside of function"),this.next(),this.eat(i.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},j.parseSwitchStatement=function(e){this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(i.braceL),this.labels.push(Na),this.enterScope(0);for(var t,r=!1;this.type!==i.braceR;)if(this.type===i._case||this.type===i._default){var s=this.type===i._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),s?t.test=this.parseExpression():(r&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),r=!0,t.test=null),this.expect(i.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},j.parseThrowStatement=function(e){return this.next(),_.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var Da=[];j.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===i._catch){var t=this.startNode();if(this.next(),this.eat(i.parenL)){t.param=this.parseBindingAtom();var r=t.param.type==="Identifier";this.enterScope(r?pr:0),this.checkLValPattern(t.param,r?mr:fe),this.expect(i.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0);t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(i._finally)?this.parseBlock():null,!e.handler&&!e.finalizer&&this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},j.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},j.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(xt),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},j.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},j.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},j.parseLabeledStatement=function(e,t,r,s){for(var a=0,h=this.labels;a<h.length;a+=1){var c=h[a];c.name===t&&this.raise(r.start,"Label '"+t+"' is already declared")}for(var d=this.type.isLoop?"loop":this.type===i._switch?"switch":null,x=this.labels.length-1;x>=0;x--){var U=this.labels[x];if(U.statementStart===e.start)U.statementStart=this.start,U.kind=d;else break}return this.labels.push({name:t,kind:d,statementStart:this.start}),e.body=this.parseStatement(s?s.indexOf("label")===-1?s+"label":s:"label"),this.labels.pop(),e.label=r,this.finishNode(e,"LabeledStatement")},j.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},j.parseBlock=function(e,t,r){for(e===void 0&&(e=!0),t===void 0&&(t=this.startNode()),t.body=[],this.expect(i.braceL),e&&this.enterScope(0);this.type!==i.braceR;){var s=this.parseStatement(null);t.body.push(s)}return r&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},j.parseFor=function(e,t){return e.init=t,this.expect(i.semi),e.test=this.type===i.semi?null:this.parseExpression(),this.expect(i.semi),e.update=this.type===i.parenR?null:this.parseExpression(),this.expect(i.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},j.parseForIn=function(e,t){var r=this.type===i._in;return this.next(),t.type==="VariableDeclaration"&&t.declarations[0].init!=null&&(!r||this.options.ecmaVersion<8||this.strict||t.kind!=="var"||t.declarations[0].id.type!=="Identifier")&&this.raise(t.start,(r?"for-in":"for-of")+" loop variable declaration may not have an initializer"),e.left=t,e.right=r?this.parseExpression():this.parseMaybeAssign(),this.expect(i.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,r?"ForInStatement":"ForOfStatement")},j.parseVar=function(e,t,r){for(e.declarations=[],e.kind=r;;){var s=this.startNode();if(this.parseVarId(s,r),this.eat(i.eq)?s.init=this.parseMaybeAssign(t):r==="const"&&!(this.type===i._in||this.options.ecmaVersion>=6&&this.isContextual("of"))?this.unexpected():s.id.type!=="Identifier"&&!(t&&(this.type===i._in||this.isContextual("of")))?this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):s.init=null,e.declarations.push(this.finishNode(s,"VariableDeclarator")),!this.eat(i.comma))break}return e},j.parseVarId=function(e,t){e.id=this.parseBindingAtom(),this.checkLValPattern(e.id,t==="var"?gt:fe,!1)};var Le=1,yt=2,gr=4;j.parseFunction=function(e,t,r,s,a){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!s)&&(this.type===i.star&&t&yt&&this.unexpected(),e.generator=this.eat(i.star)),this.options.ecmaVersion>=8&&(e.async=!!s),t&Le&&(e.id=t&gr&&this.type!==i.name?null:this.parseIdent(),e.id&&!(t&yt)&&this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?gt:fe:dr));var h=this.yieldPos,c=this.awaitPos,d=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(vt(e.async,e.generator)),t&Le||(e.id=this.type===i.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,r,!1,a),this.yieldPos=h,this.awaitPos=c,this.awaitIdentPos=d,this.finishNode(e,t&Le?"FunctionDeclaration":"FunctionExpression")},j.parseFunctionParams=function(e){this.expect(i.parenL),e.params=this.parseBindingList(i.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},j.parseClass=function(e,t){this.next();var r=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var s=this.enterClassBody(),a=this.startNode(),h=!1;for(a.body=[],this.expect(i.braceL);this.type!==i.braceR;){var c=this.parseClassElement(e.superClass!==null);c&&(a.body.push(c),c.type==="MethodDefinition"&&c.kind==="constructor"?(h&&this.raise(c.start,"Duplicate constructor in the same class"),h=!0):c.key&&c.key.type==="PrivateIdentifier"&&Oa(s,c)&&this.raiseRecoverable(c.key.start,"Identifier '#"+c.key.name+"' has already been declared"))}return this.strict=r,this.next(),e.body=this.finishNode(a,"ClassBody"),this.exitClassBody(),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},j.parseClassElement=function(e){if(this.eat(i.semi))return null;var t=this.options.ecmaVersion,r=this.startNode(),s="",a=!1,h=!1,c="method",d=!1;if(this.eatContextual("static")){if(t>=13&&this.eat(i.braceL))return this.parseClassStaticBlock(r),r;this.isClassElementNameStart()||this.type===i.star?d=!0:s="static"}if(r.static=d,!s&&t>=8&&this.eatContextual("async")&&((this.isClassElementNameStart()||this.type===i.star)&&!this.canInsertSemicolon()?h=!0:s="async"),!s&&(t>=9||!h)&&this.eat(i.star)&&(a=!0),!s&&!h&&!a){var x=this.value;(this.eatContextual("get")||this.eatContextual("set"))&&(this.isClassElementNameStart()?c=x:s=x)}if(s?(r.computed=!1,r.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc),r.key.name=s,this.finishNode(r.key,"Identifier")):this.parseClassElementName(r),t<13||this.type===i.parenL||c!=="method"||a||h){var U=!r.static&&Ge(r,"constructor"),W=U&&e;U&&c!=="method"&&this.raise(r.key.start,"Constructor can't have get/set modifier"),r.kind=U?"constructor":c,this.parseClassMethod(r,a,h,W)}else this.parseClassField(r);return r},j.isClassElementNameStart=function(){return this.type===i.name||this.type===i.privateId||this.type===i.num||this.type===i.string||this.type===i.bracketL||this.type.keyword},j.parseClassElementName=function(e){this.type===i.privateId?(this.value==="constructor"&&this.raise(this.start,"Classes can't have an element named '#constructor'"),e.computed=!1,e.key=this.parsePrivateIdent()):this.parsePropertyName(e)},j.parseClassMethod=function(e,t,r,s){var a=e.key;e.kind==="constructor"?(t&&this.raise(a.start,"Constructor can't be a generator"),r&&this.raise(a.start,"Constructor can't be an async method")):e.static&&Ge(e,"prototype")&&this.raise(a.start,"Classes may not have a static property named prototype");var h=e.value=this.parseMethod(t,r,s);return e.kind==="get"&&h.params.length!==0&&this.raiseRecoverable(h.start,"getter should have no params"),e.kind==="set"&&h.params.length!==1&&this.raiseRecoverable(h.start,"setter should have exactly one param"),e.kind==="set"&&h.params[0].type==="RestElement"&&this.raiseRecoverable(h.params[0].start,"Setter cannot use rest params"),this.finishNode(e,"MethodDefinition")},j.parseClassField=function(e){if(Ge(e,"constructor")?this.raise(e.key.start,"Classes can't have a field named 'constructor'"):e.static&&Ge(e,"prototype")&&this.raise(e.key.start,"Classes can't have a static field named 'prototype'"),this.eat(i.eq)){var t=this.currentThisScope(),r=t.inClassFieldInit;t.inClassFieldInit=!0,e.value=this.parseMaybeAssign(),t.inClassFieldInit=r}else e.value=null;return this.semicolon(),this.finishNode(e,"PropertyDefinition")},j.parseClassStaticBlock=function(e){e.body=[];var t=this.labels;for(this.labels=[],this.enterScope(Oe|dt);this.type!==i.braceR;){var r=this.parseStatement(null);e.body.push(r)}return this.next(),this.exitScope(),this.labels=t,this.finishNode(e,"StaticBlock")},j.parseClassId=function(e,t){this.type===i.name?(e.id=this.parseIdent(),t&&this.checkLValSimple(e.id,fe,!1)):(t===!0&&this.unexpected(),e.id=null)},j.parseClassSuper=function(e){e.superClass=this.eat(i._extends)?this.parseExprSubscripts(!1):null},j.enterClassBody=function(){var e={declared:Object.create(null),used:[]};return this.privateNameStack.push(e),e.declared},j.exitClassBody=function(){for(var e=this.privateNameStack.pop(),t=e.declared,r=e.used,s=this.privateNameStack.length,a=s===0?null:this.privateNameStack[s-1],h=0;h<r.length;++h){var c=r[h];D(t,c.name)||(a?a.used.push(c):this.raiseRecoverable(c.start,"Private field '#"+c.name+"' must be declared in an enclosing class"))}};function Oa(e,t){var r=t.key.name,s=e[r],a="true";return t.type==="MethodDefinition"&&(t.kind==="get"||t.kind==="set")&&(a=(t.static?"s":"i")+t.kind),s==="iget"&&a==="iset"||s==="iset"&&a==="iget"||s==="sget"&&a==="sset"||s==="sset"&&a==="sget"?(e[r]="true",!1):s?!0:(e[r]=a,!1)}function Ge(e,t){var r=e.computed,s=e.key;return!r&&(s.type==="Identifier"&&s.name===t||s.type==="Literal"&&s.value===t)}j.parseExport=function(e,t){if(this.next(),this.eat(i.star))return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseModuleExportName(),this.checkExport(t,e.exported.name,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==i.string&&this.unexpected(),e.source=this.parseExprAtom(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(i._default)){this.checkExport(t,"default",this.lastTokStart);var r;if(this.type===i._function||(r=this.isAsyncFunction())){var s=this.startNode();this.next(),r&&this.next(),e.declaration=this.parseFunction(s,Le|gr,!1,r)}else if(this.type===i._class){var a=this.startNode();e.declaration=this.parseClass(a,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(null),e.declaration.type==="VariableDeclaration"?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id.name,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==i.string&&this.unexpected(),e.source=this.parseExprAtom();else{for(var h=0,c=e.specifiers;h<c.length;h+=1){var d=c[h];this.checkUnreserved(d.local),this.checkLocalExport(d.local),d.local.type==="Literal"&&this.raise(d.local.start,"A string literal cannot be used as an exported binding without `from`.")}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},j.checkExport=function(e,t,r){!e||(D(e,t)&&this.raiseRecoverable(r,"Duplicate export '"+t+"'"),e[t]=!0)},j.checkPatternExport=function(e,t){var r=t.type;if(r==="Identifier")this.checkExport(e,t.name,t.start);else if(r==="ObjectPattern")for(var s=0,a=t.properties;s<a.length;s+=1){var h=a[s];this.checkPatternExport(e,h)}else if(r==="ArrayPattern")for(var c=0,d=t.elements;c<d.length;c+=1){var x=d[c];x&&this.checkPatternExport(e,x)}else r==="Property"?this.checkPatternExport(e,t.value):r==="AssignmentPattern"?this.checkPatternExport(e,t.left):r==="RestElement"?this.checkPatternExport(e,t.argument):r==="ParenthesizedExpression"&&this.checkPatternExport(e,t.expression)},j.checkVariableExport=function(e,t){if(!!e)for(var r=0,s=t;r<s.length;r+=1){var a=s[r];this.checkPatternExport(e,a.id)}},j.shouldParseExportStatement=function(){return this.type.keyword==="var"||this.type.keyword==="const"||this.type.keyword==="class"||this.type.keyword==="function"||this.isLet()||this.isAsyncFunction()},j.parseExportSpecifiers=function(e){var t=[],r=!0;for(this.expect(i.braceL);!this.eat(i.braceR);){if(r)r=!1;else if(this.expect(i.comma),this.afterTrailingComma(i.braceR))break;var s=this.startNode();s.local=this.parseModuleExportName(),s.exported=this.eatContextual("as")?this.parseModuleExportName():s.local,this.checkExport(e,s.exported[s.exported.type==="Identifier"?"name":"value"],s.exported.start),t.push(this.finishNode(s,"ExportSpecifier"))}return t},j.parseImport=function(e){return this.next(),this.type===i.string?(e.specifiers=Da,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===i.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},j.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===i.name){var r=this.startNode();if(r.local=this.parseIdent(),this.checkLValSimple(r.local,fe),e.push(this.finishNode(r,"ImportDefaultSpecifier")),!this.eat(i.comma))return e}if(this.type===i.star){var s=this.startNode();return this.next(),this.expectContextual("as"),s.local=this.parseIdent(),this.checkLValSimple(s.local,fe),e.push(this.finishNode(s,"ImportNamespaceSpecifier")),e}for(this.expect(i.braceL);!this.eat(i.braceR);){if(t)t=!1;else if(this.expect(i.comma),this.afterTrailingComma(i.braceR))break;var a=this.startNode();a.imported=this.parseModuleExportName(),this.eatContextual("as")?a.local=this.parseIdent():(this.checkUnreserved(a.imported),a.local=a.imported),this.checkLValSimple(a.local,fe),e.push(this.finishNode(a,"ImportSpecifier"))}return e},j.parseModuleExportName=function(){if(this.options.ecmaVersion>=13&&this.type===i.string){var e=this.parseLiteral(this.value);return F.test(e.value)&&this.raise(e.start,"An export name cannot include a lone surrogate."),e}return this.parseIdent(!0)},j.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},j.isDirectiveCandidate=function(e){return e.type==="ExpressionStatement"&&e.expression.type==="Literal"&&typeof e.expression.value=="string"&&(this.input[e.start]==='"'||this.input[e.start]==="'")};var ue=Z.prototype;ue.toAssignable=function(e,t,r){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&e.name==="await"&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",r&&this.checkPatternErrors(r,!0);for(var s=0,a=e.properties;s<a.length;s+=1){var h=a[s];this.toAssignable(h,t),h.type==="RestElement"&&(h.argument.type==="ArrayPattern"||h.argument.type==="ObjectPattern")&&this.raise(h.argument.start,"Unexpected token")}break;case"Property":e.kind!=="init"&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",r&&this.checkPatternErrors(r,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),e.argument.type==="AssignmentPattern"&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":e.operator!=="="&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,r);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else r&&this.checkPatternErrors(r,!0);return e},ue.toAssignableList=function(e,t){for(var r=e.length,s=0;s<r;s++){var a=e[s];a&&this.toAssignable(a,t)}if(r){var h=e[r-1];this.options.ecmaVersion===6&&t&&h&&h.type==="RestElement"&&h.argument.type!=="Identifier"&&this.unexpected(h.argument.start)}return e},ue.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},ue.parseRestBinding=function(){var e=this.startNode();return this.next(),this.options.ecmaVersion===6&&this.type!==i.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},ue.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case i.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(i.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case i.braceL:return this.parseObj(!0)}return this.parseIdent()},ue.parseBindingList=function(e,t,r){for(var s=[],a=!0;!this.eat(e);)if(a?a=!1:this.expect(i.comma),t&&this.type===i.comma)s.push(null);else{if(r&&this.afterTrailingComma(e))break;if(this.type===i.ellipsis){var h=this.parseRestBinding();this.parseBindingListItem(h),s.push(h),this.type===i.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}else{var c=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(c),s.push(c)}}return s},ue.parseBindingListItem=function(e){return e},ue.parseMaybeDefault=function(e,t,r){if(r=r||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(i.eq))return r;var s=this.startNodeAt(e,t);return s.left=r,s.right=this.parseMaybeAssign(),this.finishNode(s,"AssignmentPattern")},ue.checkLValSimple=function(e,t,r){t===void 0&&(t=ze);var s=t!==ze;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(s?"Binding ":"Assigning to ")+e.name+" in strict mode"),s&&(t===fe&&e.name==="let"&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),r&&(D(r,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),r[e.name]=!0),t!==vr&&this.declareName(e.name,t,e.start));break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":s&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ParenthesizedExpression":return s&&this.raiseRecoverable(e.start,"Binding parenthesized expression"),this.checkLValSimple(e.expression,t,r);default:this.raise(e.start,(s?"Binding":"Assigning to")+" rvalue")}},ue.checkLValPattern=function(e,t,r){switch(t===void 0&&(t=ze),e.type){case"ObjectPattern":for(var s=0,a=e.properties;s<a.length;s+=1){var h=a[s];this.checkLValInnerPattern(h,t,r)}break;case"ArrayPattern":for(var c=0,d=e.elements;c<d.length;c+=1){var x=d[c];x&&this.checkLValInnerPattern(x,t,r)}break;default:this.checkLValSimple(e,t,r)}},ue.checkLValInnerPattern=function(e,t,r){switch(t===void 0&&(t=ze),e.type){case"Property":this.checkLValInnerPattern(e.value,t,r);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,r);break;case"RestElement":this.checkLValPattern(e.argument,t,r);break;default:this.checkLValPattern(e,t,r)}};var ne=function(t,r,s,a,h){this.token=t,this.isExpr=!!r,this.preserveSpace=!!s,this.override=a,this.generator=!!h},Q={b_stat:new ne("{",!1),b_expr:new ne("{",!0),b_tmpl:new ne("${",!1),p_stat:new ne("(",!1),p_expr:new ne("(",!0),q_tmpl:new ne("`",!0,!0,function(e){return e.tryReadTemplateToken()}),f_stat:new ne("function",!1),f_expr:new ne("function",!0),f_expr_gen:new ne("function",!0,!1,null,!0),f_gen:new ne("function",!1,!1,null,!0)},ke=Z.prototype;ke.initialContext=function(){return[Q.b_stat]},ke.curContext=function(){return this.context[this.context.length-1]},ke.braceIsBlock=function(e){var t=this.curContext();return t===Q.f_expr||t===Q.f_stat?!0:e===i.colon&&(t===Q.b_stat||t===Q.b_expr)?!t.isExpr:e===i._return||e===i.name&&this.exprAllowed?_.test(this.input.slice(this.lastTokEnd,this.start)):e===i._else||e===i.semi||e===i.eof||e===i.parenR||e===i.arrow?!0:e===i.braceL?t===Q.b_stat:e===i._var||e===i._const||e===i.name?!1:!this.exprAllowed},ke.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if(t.token==="function")return t.generator}return!1},ke.updateContext=function(e){var t,r=this.type;r.keyword&&e===i.dot?this.exprAllowed=!1:(t=r.updateContext)?t.call(this,e):this.exprAllowed=r.beforeExpr},ke.overrideContext=function(e){this.curContext()!==e&&(this.context[this.context.length-1]=e)},i.parenR.updateContext=i.braceR.updateContext=function(){if(this.context.length===1){this.exprAllowed=!0;return}var e=this.context.pop();e===Q.b_stat&&this.curContext().token==="function"&&(e=this.context.pop()),this.exprAllowed=!e.isExpr},i.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?Q.b_stat:Q.b_expr),this.exprAllowed=!0},i.dollarBraceL.updateContext=function(){this.context.push(Q.b_tmpl),this.exprAllowed=!0},i.parenL.updateContext=function(e){var t=e===i._if||e===i._for||e===i._with||e===i._while;this.context.push(t?Q.p_stat:Q.p_expr),this.exprAllowed=!0},i.incDec.updateContext=function(){},i._function.updateContext=i._class.updateContext=function(e){e.beforeExpr&&e!==i._else&&!(e===i.semi&&this.curContext()!==Q.p_stat)&&!(e===i._return&&_.test(this.input.slice(this.lastTokEnd,this.start)))&&!((e===i.colon||e===i.braceL)&&this.curContext()===Q.b_stat)?this.context.push(Q.f_expr):this.context.push(Q.f_stat),this.exprAllowed=!1},i.backQuote.updateContext=function(){this.curContext()===Q.q_tmpl?this.context.pop():this.context.push(Q.q_tmpl),this.exprAllowed=!1},i.star.updateContext=function(e){if(e===i._function){var t=this.context.length-1;this.context[t]===Q.f_expr?this.context[t]=Q.f_expr_gen:this.context[t]=Q.f_gen}this.exprAllowed=!0},i.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==i.dot&&(this.value==="of"&&!this.exprAllowed||this.value==="yield"&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var z=Z.prototype;z.checkPropClash=function(e,t,r){if(!(this.options.ecmaVersion>=9&&e.type==="SpreadElement")&&!(this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var s=e.key,a;switch(s.type){case"Identifier":a=s.name;break;case"Literal":a=String(s.value);break;default:return}var h=e.kind;if(this.options.ecmaVersion>=6){a==="__proto__"&&h==="init"&&(t.proto&&(r?r.doubleProto<0&&(r.doubleProto=s.start):this.raiseRecoverable(s.start,"Redefinition of __proto__ property")),t.proto=!0);return}a="$"+a;var c=t[a];if(c){var d;h==="init"?d=this.strict&&c.init||c.get||c.set:d=c.init||c[h],d&&this.raiseRecoverable(s.start,"Redefinition of property")}else c=t[a]={init:!1,get:!1,set:!1};c[h]=!0}},z.parseExpression=function(e,t){var r=this.start,s=this.startLoc,a=this.parseMaybeAssign(e,t);if(this.type===i.comma){var h=this.startNodeAt(r,s);for(h.expressions=[a];this.eat(i.comma);)h.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(h,"SequenceExpression")}return a},z.parseMaybeAssign=function(e,t,r){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var s=!1,a=-1,h=-1,c=-1;t?(a=t.parenthesizedAssign,h=t.trailingComma,c=t.doubleProto,t.parenthesizedAssign=t.trailingComma=-1):(t=new We,s=!0);var d=this.start,x=this.startLoc;(this.type===i.parenL||this.type===i.name)&&(this.potentialArrowAt=this.start,this.potentialArrowInForAwait=e==="await");var U=this.parseMaybeConditional(e,t);if(r&&(U=r.call(this,U,d,x)),this.type.isAssign){var W=this.startNodeAt(d,x);return W.operator=this.value,this.type===i.eq&&(U=this.toAssignable(U,!1,t)),s||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=U.start&&(t.shorthandAssign=-1),this.type===i.eq?this.checkLValPattern(U):this.checkLValSimple(U),W.left=U,this.next(),W.right=this.parseMaybeAssign(e),c>-1&&(t.doubleProto=c),this.finishNode(W,"AssignmentExpression")}else s&&this.checkExpressionErrors(t,!0);return a>-1&&(t.parenthesizedAssign=a),h>-1&&(t.trailingComma=h),U},z.parseMaybeConditional=function(e,t){var r=this.start,s=this.startLoc,a=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return a;if(this.eat(i.question)){var h=this.startNodeAt(r,s);return h.test=a,h.consequent=this.parseMaybeAssign(),this.expect(i.colon),h.alternate=this.parseMaybeAssign(e),this.finishNode(h,"ConditionalExpression")}return a},z.parseExprOps=function(e,t){var r=this.start,s=this.startLoc,a=this.parseMaybeUnary(t,!1,!1,e);return this.checkExpressionErrors(t)||a.start===r&&a.type==="ArrowFunctionExpression"?a:this.parseExprOp(a,r,s,-1,e)},z.parseExprOp=function(e,t,r,s,a){var h=this.type.binop;if(h!=null&&(!a||this.type!==i._in)&&h>s){var c=this.type===i.logicalOR||this.type===i.logicalAND,d=this.type===i.coalesce;d&&(h=i.logicalAND.binop);var x=this.value;this.next();var U=this.start,W=this.startLoc,re=this.parseExprOp(this.parseMaybeUnary(null,!1,!1,a),U,W,h,a),be=this.buildBinary(t,r,e,re,x,c||d);return(c&&this.type===i.coalesce||d&&(this.type===i.logicalOR||this.type===i.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(be,t,r,s,a)}return e},z.buildBinary=function(e,t,r,s,a,h){s.type==="PrivateIdentifier"&&this.raise(s.start,"Private identifier can only be left side of binary expression");var c=this.startNodeAt(e,t);return c.left=r,c.operator=a,c.right=s,this.finishNode(c,h?"LogicalExpression":"BinaryExpression")},z.parseMaybeUnary=function(e,t,r,s){var a=this.start,h=this.startLoc,c;if(this.isContextual("await")&&this.canAwait)c=this.parseAwait(s),t=!0;else if(this.type.prefix){var d=this.startNode(),x=this.type===i.incDec;d.operator=this.value,d.prefix=!0,this.next(),d.argument=this.parseMaybeUnary(null,!0,x,s),this.checkExpressionErrors(e,!0),x?this.checkLValSimple(d.argument):this.strict&&d.operator==="delete"&&d.argument.type==="Identifier"?this.raiseRecoverable(d.start,"Deleting local variable in strict mode"):d.operator==="delete"&&xr(d.argument)?this.raiseRecoverable(d.start,"Private fields can not be deleted"):t=!0,c=this.finishNode(d,x?"UpdateExpression":"UnaryExpression")}else if(!t&&this.type===i.privateId)(s||this.privateNameStack.length===0)&&this.unexpected(),c=this.parsePrivateIdent(),this.type!==i._in&&this.unexpected();else{if(c=this.parseExprSubscripts(e,s),this.checkExpressionErrors(e))return c;for(;this.type.postfix&&!this.canInsertSemicolon();){var U=this.startNodeAt(a,h);U.operator=this.value,U.prefix=!1,U.argument=c,this.checkLValSimple(c),this.next(),c=this.finishNode(U,"UpdateExpression")}}if(!r&&this.eat(i.starstar))if(t)this.unexpected(this.lastTokStart);else return this.buildBinary(a,h,c,this.parseMaybeUnary(null,!1,!1,s),"**",!1);else return c};function xr(e){return e.type==="MemberExpression"&&e.property.type==="PrivateIdentifier"||e.type==="ChainExpression"&&xr(e.expression)}z.parseExprSubscripts=function(e,t){var r=this.start,s=this.startLoc,a=this.parseExprAtom(e,t);if(a.type==="ArrowFunctionExpression"&&this.input.slice(this.lastTokStart,this.lastTokEnd)!==")")return a;var h=this.parseSubscripts(a,r,s,!1,t);return e&&h.type==="MemberExpression"&&(e.parenthesizedAssign>=h.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=h.start&&(e.parenthesizedBind=-1),e.trailingComma>=h.start&&(e.trailingComma=-1)),h},z.parseSubscripts=function(e,t,r,s,a){for(var h=this.options.ecmaVersion>=8&&e.type==="Identifier"&&e.name==="async"&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start===5&&this.potentialArrowAt===e.start,c=!1;;){var d=this.parseSubscript(e,t,r,s,h,c,a);if(d.optional&&(c=!0),d===e||d.type==="ArrowFunctionExpression"){if(c){var x=this.startNodeAt(t,r);x.expression=d,d=this.finishNode(x,"ChainExpression")}return d}e=d}},z.parseSubscript=function(e,t,r,s,a,h,c){var d=this.options.ecmaVersion>=11,x=d&&this.eat(i.questionDot);s&&x&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var U=this.eat(i.bracketL);if(U||x&&this.type!==i.parenL&&this.type!==i.backQuote||this.eat(i.dot)){var W=this.startNodeAt(t,r);W.object=e,U?(W.property=this.parseExpression(),this.expect(i.bracketR)):this.type===i.privateId&&e.type!=="Super"?W.property=this.parsePrivateIdent():W.property=this.parseIdent(this.options.allowReserved!=="never"),W.computed=!!U,d&&(W.optional=x),e=this.finishNode(W,"MemberExpression")}else if(!s&&this.eat(i.parenL)){var re=new We,be=this.yieldPos,je=this.awaitPos,Fe=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var Qe=this.parseExprList(i.parenR,this.options.ecmaVersion>=8,!1,re);if(a&&!x&&!this.canInsertSemicolon()&&this.eat(i.arrow))return this.checkPatternErrors(re,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=be,this.awaitPos=je,this.awaitIdentPos=Fe,this.parseArrowExpression(this.startNodeAt(t,r),Qe,!0,c);this.checkExpressionErrors(re,!0),this.yieldPos=be||this.yieldPos,this.awaitPos=je||this.awaitPos,this.awaitIdentPos=Fe||this.awaitIdentPos;var Be=this.startNodeAt(t,r);Be.callee=e,Be.arguments=Qe,d&&(Be.optional=x),e=this.finishNode(Be,"CallExpression")}else if(this.type===i.backQuote){(x||h)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var Te=this.startNodeAt(t,r);Te.tag=e,Te.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(Te,"TaggedTemplateExpression")}return e},z.parseExprAtom=function(e,t){this.type===i.slash&&this.readRegexp();var r,s=this.potentialArrowAt===this.start;switch(this.type){case i._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),r=this.startNode(),this.next(),this.type===i.parenL&&!this.allowDirectSuper&&this.raise(r.start,"super() call outside constructor of a subclass"),this.type!==i.dot&&this.type!==i.bracketL&&this.type!==i.parenL&&this.unexpected(),this.finishNode(r,"Super");case i._this:return r=this.startNode(),this.next(),this.finishNode(r,"ThisExpression");case i.name:var a=this.start,h=this.startLoc,c=this.containsEsc,d=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!c&&d.name==="async"&&!this.canInsertSemicolon()&&this.eat(i._function))return this.overrideContext(Q.f_expr),this.parseFunction(this.startNodeAt(a,h),0,!1,!0,t);if(s&&!this.canInsertSemicolon()){if(this.eat(i.arrow))return this.parseArrowExpression(this.startNodeAt(a,h),[d],!1,t);if(this.options.ecmaVersion>=8&&d.name==="async"&&this.type===i.name&&!c&&(!this.potentialArrowInForAwait||this.value!=="of"||this.containsEsc))return d=this.parseIdent(!1),(this.canInsertSemicolon()||!this.eat(i.arrow))&&this.unexpected(),this.parseArrowExpression(this.startNodeAt(a,h),[d],!0,t)}return d;case i.regexp:var x=this.value;return r=this.parseLiteral(x.value),r.regex={pattern:x.pattern,flags:x.flags},r;case i.num:case i.string:return this.parseLiteral(this.value);case i._null:case i._true:case i._false:return r=this.startNode(),r.value=this.type===i._null?null:this.type===i._true,r.raw=this.type.keyword,this.next(),this.finishNode(r,"Literal");case i.parenL:var U=this.start,W=this.parseParenAndDistinguishExpression(s,t);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(W)&&(e.parenthesizedAssign=U),e.parenthesizedBind<0&&(e.parenthesizedBind=U)),W;case i.bracketL:return r=this.startNode(),this.next(),r.elements=this.parseExprList(i.bracketR,!0,!0,e),this.finishNode(r,"ArrayExpression");case i.braceL:return this.overrideContext(Q.b_expr),this.parseObj(!1,e);case i._function:return r=this.startNode(),this.next(),this.parseFunction(r,0);case i._class:return this.parseClass(this.startNode(),!1);case i._new:return this.parseNew();case i.backQuote:return this.parseTemplate();case i._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},z.parseExprImport=function(){var e=this.startNode();this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import");var t=this.parseIdent(!0);switch(this.type){case i.parenL:return this.parseDynamicImport(e);case i.dot:return e.meta=t,this.parseImportMeta(e);default:this.unexpected()}},z.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),!this.eat(i.parenR)){var t=this.start;this.eat(i.comma)&&this.eat(i.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},z.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),e.property.name!=="meta"&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),this.options.sourceType!=="module"&&!this.options.allowImportExportEverywhere&&this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},z.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),t.raw.charCodeAt(t.raw.length-1)===110&&(t.bigint=t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},z.parseParenExpression=function(){this.expect(i.parenL);var e=this.parseExpression();return this.expect(i.parenR),e},z.parseParenAndDistinguishExpression=function(e,t){var r=this.start,s=this.startLoc,a,h=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var c=this.start,d=this.startLoc,x=[],U=!0,W=!1,re=new We,be=this.yieldPos,je=this.awaitPos,Fe;for(this.yieldPos=0,this.awaitPos=0;this.type!==i.parenR;)if(U?U=!1:this.expect(i.comma),h&&this.afterTrailingComma(i.parenR,!0)){W=!0;break}else if(this.type===i.ellipsis){Fe=this.start,x.push(this.parseParenItem(this.parseRestBinding())),this.type===i.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}else x.push(this.parseMaybeAssign(!1,re,this.parseParenItem));var Qe=this.lastTokEnd,Be=this.lastTokEndLoc;if(this.expect(i.parenR),e&&!this.canInsertSemicolon()&&this.eat(i.arrow))return this.checkPatternErrors(re,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=be,this.awaitPos=je,this.parseParenArrowList(r,s,x,t);(!x.length||W)&&this.unexpected(this.lastTokStart),Fe&&this.unexpected(Fe),this.checkExpressionErrors(re,!0),this.yieldPos=be||this.yieldPos,this.awaitPos=je||this.awaitPos,x.length>1?(a=this.startNodeAt(c,d),a.expressions=x,this.finishNodeAt(a,"SequenceExpression",Qe,Be)):a=x[0]}else a=this.parseParenExpression();if(this.options.preserveParens){var Te=this.startNodeAt(r,s);return Te.expression=a,this.finishNode(Te,"ParenthesizedExpression")}else return a},z.parseParenItem=function(e){return e},z.parseParenArrowList=function(e,t,r,s){return this.parseArrowExpression(this.startNodeAt(e,t),r,!1,s)};var La=[];z.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(i.dot)){e.meta=t;var r=this.containsEsc;return e.property=this.parseIdent(!0),e.property.name!=="target"&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),r&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.allowNewDotTarget||this.raiseRecoverable(e.start,"'new.target' can only be used in functions and class static block"),this.finishNode(e,"MetaProperty")}var s=this.start,a=this.startLoc,h=this.type===i._import;return e.callee=this.parseSubscripts(this.parseExprAtom(),s,a,!0,!1),h&&e.callee.type==="ImportExpression"&&this.raise(s,"Cannot use new with import()"),this.eat(i.parenL)?e.arguments=this.parseExprList(i.parenR,this.options.ecmaVersion>=8,!1):e.arguments=La,this.finishNode(e,"NewExpression")},z.parseTemplateElement=function(e){var t=e.isTagged,r=this.startNode();return this.type===i.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),r.value={raw:this.value,cooked:null}):r.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,`
`),cooked:this.value},this.next(),r.tail=this.type===i.backQuote,this.finishNode(r,"TemplateElement")},z.parseTemplate=function(e){e===void 0&&(e={});var t=e.isTagged;t===void 0&&(t=!1);var r=this.startNode();this.next(),r.expressions=[];var s=this.parseTemplateElement({isTagged:t});for(r.quasis=[s];!s.tail;)this.type===i.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(i.dollarBraceL),r.expressions.push(this.parseExpression()),this.expect(i.braceR),r.quasis.push(s=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(r,"TemplateLiteral")},z.isAsyncProp=function(e){return!e.computed&&e.key.type==="Identifier"&&e.key.name==="async"&&(this.type===i.name||this.type===i.num||this.type===i.string||this.type===i.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===i.star)&&!_.test(this.input.slice(this.lastTokEnd,this.start))},z.parseObj=function(e,t){var r=this.startNode(),s=!0,a={};for(r.properties=[],this.next();!this.eat(i.braceR);){if(s)s=!1;else if(this.expect(i.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(i.braceR))break;var h=this.parseProperty(e,t);e||this.checkPropClash(h,a,t),r.properties.push(h)}return this.finishNode(r,e?"ObjectPattern":"ObjectExpression")},z.parseProperty=function(e,t){var r=this.startNode(),s,a,h,c;if(this.options.ecmaVersion>=9&&this.eat(i.ellipsis))return e?(r.argument=this.parseIdent(!1),this.type===i.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(r,"RestElement")):(this.type===i.parenL&&t&&(t.parenthesizedAssign<0&&(t.parenthesizedAssign=this.start),t.parenthesizedBind<0&&(t.parenthesizedBind=this.start)),r.argument=this.parseMaybeAssign(!1,t),this.type===i.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(r,"SpreadElement"));this.options.ecmaVersion>=6&&(r.method=!1,r.shorthand=!1,(e||t)&&(h=this.start,c=this.startLoc),e||(s=this.eat(i.star)));var d=this.containsEsc;return this.parsePropertyName(r),!e&&!d&&this.options.ecmaVersion>=8&&!s&&this.isAsyncProp(r)?(a=!0,s=this.options.ecmaVersion>=9&&this.eat(i.star),this.parsePropertyName(r,t)):a=!1,this.parsePropertyValue(r,e,s,a,h,c,t,d),this.finishNode(r,"Property")},z.parsePropertyValue=function(e,t,r,s,a,h,c,d){if((r||s)&&this.type===i.colon&&this.unexpected(),this.eat(i.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,c),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===i.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(r,s);else if(!t&&!d&&this.options.ecmaVersion>=5&&!e.computed&&e.key.type==="Identifier"&&(e.key.name==="get"||e.key.name==="set")&&this.type!==i.comma&&this.type!==i.braceR&&this.type!==i.eq){(r||s)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var x=e.kind==="get"?0:1;if(e.value.params.length!==x){var U=e.value.start;e.kind==="get"?this.raiseRecoverable(U,"getter should have no params"):this.raiseRecoverable(U,"setter should have exactly one param")}else e.kind==="set"&&e.value.params[0].type==="RestElement"&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}else this.options.ecmaVersion>=6&&!e.computed&&e.key.type==="Identifier"?((r||s)&&this.unexpected(),this.checkUnreserved(e.key),e.key.name==="await"&&!this.awaitIdentPos&&(this.awaitIdentPos=a),e.kind="init",t?e.value=this.parseMaybeDefault(a,h,this.copyNode(e.key)):this.type===i.eq&&c?(c.shorthandAssign<0&&(c.shorthandAssign=this.start),e.value=this.parseMaybeDefault(a,h,this.copyNode(e.key))):e.value=this.copyNode(e.key),e.shorthand=!0):this.unexpected()},z.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(i.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(i.bracketR),e.key;e.computed=!1}return e.key=this.type===i.num||this.type===i.string?this.parseExprAtom():this.parseIdent(this.options.allowReserved!=="never")},z.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},z.parseMethod=function(e,t,r){var s=this.startNode(),a=this.yieldPos,h=this.awaitPos,c=this.awaitIdentPos;return this.initFunction(s),this.options.ecmaVersion>=6&&(s.generator=e),this.options.ecmaVersion>=8&&(s.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(vt(t,s.generator)|dt|(r?fr:0)),this.expect(i.parenL),s.params=this.parseBindingList(i.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(s,!1,!0,!1),this.yieldPos=a,this.awaitPos=h,this.awaitIdentPos=c,this.finishNode(s,"FunctionExpression")},z.parseArrowExpression=function(e,t,r,s){var a=this.yieldPos,h=this.awaitPos,c=this.awaitIdentPos;return this.enterScope(vt(r,!1)|cr),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!r),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1,s),this.yieldPos=a,this.awaitPos=h,this.awaitIdentPos=c,this.finishNode(e,"ArrowFunctionExpression")},z.parseFunctionBody=function(e,t,r,s){var a=t&&this.type!==i.braceL,h=this.strict,c=!1;if(a)e.body=this.parseMaybeAssign(s),e.expression=!0,this.checkParams(e,!1);else{var d=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);(!h||d)&&(c=this.strictDirective(this.end),c&&d&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list"));var x=this.labels;this.labels=[],c&&(this.strict=!0),this.checkParams(e,!h&&!c&&!t&&!r&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLValSimple(e.id,vr),e.body=this.parseBlock(!1,void 0,c&&!h),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=x}this.exitScope()},z.isSimpleParamList=function(e){for(var t=0,r=e;t<r.length;t+=1){var s=r[t];if(s.type!=="Identifier")return!1}return!0},z.checkParams=function(e,t){for(var r=Object.create(null),s=0,a=e.params;s<a.length;s+=1){var h=a[s];this.checkLValInnerPattern(h,gt,t?null:r)}},z.parseExprList=function(e,t,r,s){for(var a=[],h=!0;!this.eat(e);){if(h)h=!1;else if(this.expect(i.comma),t&&this.afterTrailingComma(e))break;var c=void 0;r&&this.type===i.comma?c=null:this.type===i.ellipsis?(c=this.parseSpread(s),s&&this.type===i.comma&&s.trailingComma<0&&(s.trailingComma=this.start)):c=this.parseMaybeAssign(!1,s),a.push(c)}return a},z.checkUnreserved=function(e){var t=e.start,r=e.end,s=e.name;if(this.inGenerator&&s==="yield"&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&s==="await"&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.currentThisScope().inClassFieldInit&&s==="arguments"&&this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer"),this.inClassStaticBlock&&(s==="arguments"||s==="await")&&this.raise(t,"Cannot use "+s+" in class static initialization block"),this.keywords.test(s)&&this.raise(t,"Unexpected keyword '"+s+"'"),!(this.options.ecmaVersion<6&&this.input.slice(t,r).indexOf("\\")!==-1)){var a=this.strict?this.reservedWordsStrict:this.reservedWords;a.test(s)&&(!this.inAsync&&s==="await"&&this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+s+"' is reserved"))}},z.parseIdent=function(e,t){var r=this.startNode();return this.type===i.name?r.name=this.value:this.type.keyword?(r.name=this.type.keyword,(r.name==="class"||r.name==="function")&&(this.lastTokEnd!==this.lastTokStart+1||this.input.charCodeAt(this.lastTokStart)!==46)&&this.context.pop()):this.unexpected(),this.next(!!e),this.finishNode(r,"Identifier"),e||(this.checkUnreserved(r),r.name==="await"&&!this.awaitIdentPos&&(this.awaitIdentPos=r.start)),r},z.parsePrivateIdent=function(){var e=this.startNode();return this.type===i.privateId?e.name=this.value:this.unexpected(),this.next(),this.finishNode(e,"PrivateIdentifier"),this.privateNameStack.length===0?this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class"):this.privateNameStack[this.privateNameStack.length-1].used.push(e),e},z.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===i.semi||this.canInsertSemicolon()||this.type!==i.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(i.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},z.parseAwait=function(e){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0,!1,e),this.finishNode(t,"AwaitExpression")};var Ke=Z.prototype;Ke.raise=function(e,t){var r=oe(this.input,e);t+=" ("+r.line+":"+r.column+")";var s=new SyntaxError(t);throw s.pos=e,s.loc=r,s.raisedAt=this.pos,s},Ke.raiseRecoverable=Ke.raise,Ke.curPosition=function(){if(this.options.locations)return new K(this.curLine,this.pos-this.lineStart)};var xe=Z.prototype,Va=function(t){this.flags=t,this.var=[],this.lexical=[],this.functions=[],this.inClassFieldInit=!1};xe.enterScope=function(e){this.scopeStack.push(new Va(e))},xe.exitScope=function(){this.scopeStack.pop()},xe.treatFunctionsAsVarInScope=function(e){return e.flags&ge||!this.inModule&&e.flags&Ee},xe.declareName=function(e,t,r){var s=!1;if(t===fe){var a=this.currentScope();s=a.lexical.indexOf(e)>-1||a.functions.indexOf(e)>-1||a.var.indexOf(e)>-1,a.lexical.push(e),this.inModule&&a.flags&Ee&&delete this.undefinedExports[e]}else if(t===mr){var h=this.currentScope();h.lexical.push(e)}else if(t===dr){var c=this.currentScope();this.treatFunctionsAsVar?s=c.lexical.indexOf(e)>-1:s=c.lexical.indexOf(e)>-1||c.var.indexOf(e)>-1,c.functions.push(e)}else for(var d=this.scopeStack.length-1;d>=0;--d){var x=this.scopeStack[d];if(x.lexical.indexOf(e)>-1&&!(x.flags&pr&&x.lexical[0]===e)||!this.treatFunctionsAsVarInScope(x)&&x.functions.indexOf(e)>-1){s=!0;break}if(x.var.push(e),this.inModule&&x.flags&Ee&&delete this.undefinedExports[e],x.flags&mt)break}s&&this.raiseRecoverable(r,"Identifier '"+e+"' has already been declared")},xe.checkLocalExport=function(e){this.scopeStack[0].lexical.indexOf(e.name)===-1&&this.scopeStack[0].var.indexOf(e.name)===-1&&(this.undefinedExports[e.name]=e)},xe.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},xe.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&mt)return t}},xe.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&mt&&!(t.flags&cr))return t}};var Ve=function(t,r,s){this.type="",this.start=r,this.end=0,t.options.locations&&(this.loc=new X(t,s)),t.options.directSourceFile&&(this.sourceFile=t.options.directSourceFile),t.options.ranges&&(this.range=[r,0])},Re=Z.prototype;Re.startNode=function(){return new Ve(this,this.start,this.startLoc)},Re.startNodeAt=function(e,t){return new Ve(this,e,t)};function yr(e,t,r,s){return e.type=t,e.end=r,this.options.locations&&(e.loc.end=s),this.options.ranges&&(e.range[1]=r),e}Re.finishNode=function(e,t){return yr.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},Re.finishNodeAt=function(e,t,r,s){return yr.call(this,e,t,r,s)},Re.copyNode=function(e){var t=new Ve(this,e.start,this.startLoc);for(var r in e)t[r]=e[r];return t};var Ar="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",Cr=Ar+" Extended_Pictographic",Er=Cr,br=Er+" EBase EComp EMod EPres ExtPict",Ra=br,ja={9:Ar,10:Cr,11:Er,12:br,13:Ra},_r="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",Sr="Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",wr=Sr+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",kr=wr+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",Fr=kr+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi",qa=Fr+" Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith",Ma={9:Sr,10:wr,11:kr,12:Fr,13:qa},Br={};function Ua(e){var t=Br[e]={binary:f(ja[e]+" "+_r),nonBinary:{General_Category:f(_r),Script:f(Ma[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}for(var At=0,Tr=[9,10,11,12,13];At<Tr.length;At+=1){var za=Tr[At];Ua(za)}var O=Z.prototype,de=function(t){this.parser=t,this.validFlags="gim"+(t.options.ecmaVersion>=6?"uy":"")+(t.options.ecmaVersion>=9?"s":"")+(t.options.ecmaVersion>=13?"d":""),this.unicodeProperties=Br[t.options.ecmaVersion>=13?13:t.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};de.prototype.reset=function(t,r,s){var a=s.indexOf("u")!==-1;this.start=t|0,this.source=r+"",this.flags=s,this.switchU=a&&this.parser.options.ecmaVersion>=6,this.switchN=a&&this.parser.options.ecmaVersion>=9},de.prototype.raise=function(t){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+t)},de.prototype.at=function(t,r){r===void 0&&(r=!1);var s=this.source,a=s.length;if(t>=a)return-1;var h=s.charCodeAt(t);if(!(r||this.switchU)||h<=55295||h>=57344||t+1>=a)return h;var c=s.charCodeAt(t+1);return c>=56320&&c<=57343?(h<<10)+c-56613888:h},de.prototype.nextIndex=function(t,r){r===void 0&&(r=!1);var s=this.source,a=s.length;if(t>=a)return a;var h=s.charCodeAt(t),c;return!(r||this.switchU)||h<=55295||h>=57344||t+1>=a||(c=s.charCodeAt(t+1))<56320||c>57343?t+1:t+2},de.prototype.current=function(t){return t===void 0&&(t=!1),this.at(this.pos,t)},de.prototype.lookahead=function(t){return t===void 0&&(t=!1),this.at(this.nextIndex(this.pos,t),t)},de.prototype.advance=function(t){t===void 0&&(t=!1),this.pos=this.nextIndex(this.pos,t)},de.prototype.eat=function(t,r){return r===void 0&&(r=!1),this.current(r)===t?(this.advance(r),!0):!1};function Xe(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode((e>>10)+55296,(e&1023)+56320))}O.validateRegExpFlags=function(e){for(var t=e.validFlags,r=e.flags,s=0;s<r.length;s++){var a=r.charAt(s);t.indexOf(a)===-1&&this.raise(e.start,"Invalid regular expression flag"),r.indexOf(a,s+1)>-1&&this.raise(e.start,"Duplicate regular expression flag")}},O.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0&&(e.switchN=!0,this.regexp_pattern(e))},O.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames.length=0,e.backReferenceNames.length=0,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,r=e.backReferenceNames;t<r.length;t+=1){var s=r[t];e.groupNames.indexOf(s)===-1&&e.raise("Invalid named capture referenced")}},O.regexp_disjunction=function(e){for(this.regexp_alternative(e);e.eat(124);)this.regexp_alternative(e);this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},O.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},O.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))?(this.regexp_eatQuantifier(e),!0):!1},O.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var r=!1;if(this.options.ecmaVersion>=9&&(r=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!r,!0}return e.pos=t,!1},O.regexp_eatQuantifier=function(e,t){return t===void 0&&(t=!1),this.regexp_eatQuantifierPrefix(e,t)?(e.eat(63),!0):!1},O.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},O.regexp_eatBracedQuantifier=function(e,t){var r=e.pos;if(e.eat(123)){var s=0,a=-1;if(this.regexp_eatDecimalDigits(e)&&(s=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(a=e.lastIntValue),e.eat(125)))return a!==-1&&a<s&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=r}return!1},O.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},O.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},O.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}e.pos=t}return!1},O.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):e.current()===63&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},O.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},O.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},O.regexp_eatSyntaxCharacter=function(e){var t=e.current();return Ir(t)?(e.lastIntValue=t,e.advance(),!0):!1};function Ir(e){return e===36||e>=40&&e<=43||e===46||e===63||e>=91&&e<=94||e>=123&&e<=125}O.regexp_eatPatternCharacters=function(e){for(var t=e.pos,r=0;(r=e.current())!==-1&&!Ir(r);)e.advance();return e.pos!==t},O.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return t!==-1&&t!==36&&!(t>=40&&t<=43)&&t!==46&&t!==63&&t!==91&&t!==94&&t!==124?(e.advance(),!0):!1},O.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e)){e.groupNames.indexOf(e.lastStringValue)!==-1&&e.raise("Duplicate capture group name"),e.groupNames.push(e.lastStringValue);return}e.raise("Invalid group")}},O.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},O.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=Xe(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=Xe(e.lastIntValue);return!0}return!1},O.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,r=this.options.ecmaVersion>=11,s=e.current(r);return e.advance(r),s===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e,r)&&(s=e.lastIntValue),Wa(s)?(e.lastIntValue=s,!0):(e.pos=t,!1)};function Wa(e){return v(e,!0)||e===36||e===95}O.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,r=this.options.ecmaVersion>=11,s=e.current(r);return e.advance(r),s===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e,r)&&(s=e.lastIntValue),Ga(s)?(e.lastIntValue=s,!0):(e.pos=t,!1)};function Ga(e){return q(e,!0)||e===36||e===95||e===8204||e===8205}O.regexp_eatAtomEscape=function(e){return this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e)?!0:(e.switchU&&(e.current()===99&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},O.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var r=e.lastIntValue;if(e.switchU)return r>e.maxBackReference&&(e.maxBackReference=r),!0;if(r<=e.numCapturingParens)return!0;e.pos=t}return!1},O.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},O.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},O.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},O.regexp_eatZero=function(e){return e.current()===48&&!He(e.lookahead())?(e.lastIntValue=0,e.advance(),!0):!1},O.regexp_eatControlEscape=function(e){var t=e.current();return t===116?(e.lastIntValue=9,e.advance(),!0):t===110?(e.lastIntValue=10,e.advance(),!0):t===118?(e.lastIntValue=11,e.advance(),!0):t===102?(e.lastIntValue=12,e.advance(),!0):t===114?(e.lastIntValue=13,e.advance(),!0):!1},O.regexp_eatControlLetter=function(e){var t=e.current();return Pr(t)?(e.lastIntValue=t%32,e.advance(),!0):!1};function Pr(e){return e>=65&&e<=90||e>=97&&e<=122}O.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){t===void 0&&(t=!1);var r=e.pos,s=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var a=e.lastIntValue;if(s&&a>=55296&&a<=56319){var h=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var c=e.lastIntValue;if(c>=56320&&c<=57343)return e.lastIntValue=(a-55296)*1024+(c-56320)+65536,!0}e.pos=h,e.lastIntValue=a}return!0}if(s&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&Ka(e.lastIntValue))return!0;s&&e.raise("Invalid unicode escape"),e.pos=r}return!1};function Ka(e){return e>=0&&e<=1114111}O.regexp_eatIdentityEscape=function(e){if(e.switchU)return this.regexp_eatSyntaxCharacter(e)?!0:e.eat(47)?(e.lastIntValue=47,!0):!1;var t=e.current();return t!==99&&(!e.switchN||t!==107)?(e.lastIntValue=t,e.advance(),!0):!1},O.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do e.lastIntValue=10*e.lastIntValue+(t-48),e.advance();while((t=e.current())>=48&&t<=57);return!0}return!1},O.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(Xa(t))return e.lastIntValue=-1,e.advance(),!0;if(e.switchU&&this.options.ecmaVersion>=9&&(t===80||t===112)){if(e.lastIntValue=-1,e.advance(),e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125))return!0;e.raise("Invalid property name")}return!1};function Xa(e){return e===100||e===68||e===115||e===83||e===119||e===87}O.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var r=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var s=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,r,s),!0}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var a=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,a),!0}return!1},O.regexp_validateUnicodePropertyNameAndValue=function(e,t,r){D(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(r)||e.raise("Invalid property value")},O.regexp_validateUnicodePropertyNameOrValue=function(e,t){e.unicodeProperties.binary.test(t)||e.raise("Invalid property name")},O.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";Nr(t=e.current());)e.lastStringValue+=Xe(t),e.advance();return e.lastStringValue!==""};function Nr(e){return Pr(e)||e===95}O.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";Ha(t=e.current());)e.lastStringValue+=Xe(t),e.advance();return e.lastStringValue!==""};function Ha(e){return Nr(e)||He(e)}O.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},O.regexp_eatCharacterClass=function(e){if(e.eat(91)){if(e.eat(94),this.regexp_classRanges(e),e.eat(93))return!0;e.raise("Unterminated character class")}return!1},O.regexp_classRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var r=e.lastIntValue;e.switchU&&(t===-1||r===-1)&&e.raise("Invalid character class"),t!==-1&&r!==-1&&t>r&&e.raise("Range out of order in character class")}}},O.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var r=e.current();(r===99||Lr(r))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var s=e.current();return s!==93?(e.lastIntValue=s,e.advance(),!0):!1},O.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},O.regexp_eatClassControlLetter=function(e){var t=e.current();return He(t)||t===95?(e.lastIntValue=t%32,e.advance(),!0):!1},O.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},O.regexp_eatDecimalDigits=function(e){var t=e.pos,r=0;for(e.lastIntValue=0;He(r=e.current());)e.lastIntValue=10*e.lastIntValue+(r-48),e.advance();return e.pos!==t};function He(e){return e>=48&&e<=57}O.regexp_eatHexDigits=function(e){var t=e.pos,r=0;for(e.lastIntValue=0;Dr(r=e.current());)e.lastIntValue=16*e.lastIntValue+Or(r),e.advance();return e.pos!==t};function Dr(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function Or(e){return e>=65&&e<=70?10+(e-65):e>=97&&e<=102?10+(e-97):e-48}O.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var r=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=t*64+r*8+e.lastIntValue:e.lastIntValue=t*8+r}else e.lastIntValue=t;return!0}return!1},O.regexp_eatOctalDigit=function(e){var t=e.current();return Lr(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)};function Lr(e){return e>=48&&e<=55}O.regexp_eatFixedHexDigits=function(e,t){var r=e.pos;e.lastIntValue=0;for(var s=0;s<t;++s){var a=e.current();if(!Dr(a))return e.pos=r,!1;e.lastIntValue=16*e.lastIntValue+Or(a),e.advance()}return!0};var Je=function(t){this.type=t.type,this.value=t.value,this.start=t.start,this.end=t.end,t.options.locations&&(this.loc=new X(t,t.startLoc,t.endLoc)),t.options.ranges&&(this.range=[t.start,t.end])},G=Z.prototype;G.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new Je(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},G.getToken=function(){return this.next(),new Je(this)},typeof Symbol<"u"&&(G[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===i.eof,value:t}}}}),G.nextToken=function(){var e=this.curContext();if((!e||!e.preserveSpace)&&this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length)return this.finishToken(i.eof);if(e.override)return e.override(this);this.readToken(this.fullCharCodeAtPos())},G.readToken=function(e){return v(e,this.options.ecmaVersion>=6)||e===92?this.readWord():this.getTokenFromCode(e)},G.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320)return e;var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-56613888},G.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,r=this.input.indexOf("*/",this.pos+=2);if(r===-1&&this.raise(this.pos-2,"Unterminated comment"),this.pos=r+2,this.options.locations)for(var s=void 0,a=t;(s=ee(this.input,a,this.pos))>-1;)++this.curLine,a=this.lineStart=s;this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,r),t,this.pos,e,this.curPosition())},G.skipLineComment=function(e){for(var t=this.pos,r=this.options.onComment&&this.curPosition(),s=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!M(s);)s=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,r,this.curPosition())},G.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:this.input.charCodeAt(this.pos+1)===10&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(e>8&&e<14||e>=5760&&ae.test(String.fromCharCode(e)))++this.pos;else break e}}},G.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var r=this.type;this.type=e,this.value=t,this.updateContext(r)},G.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&e===46&&t===46?(this.pos+=3,this.finishToken(i.ellipsis)):(++this.pos,this.finishToken(i.dot))},G.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):e===61?this.finishOp(i.assign,2):this.finishOp(i.slash,1)},G.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),r=1,s=e===42?i.star:i.modulo;return this.options.ecmaVersion>=7&&e===42&&t===42&&(++r,s=i.starstar,t=this.input.charCodeAt(this.pos+2)),t===61?this.finishOp(i.assign,r+1):this.finishOp(s,r)},G.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){if(this.options.ecmaVersion>=12){var r=this.input.charCodeAt(this.pos+2);if(r===61)return this.finishOp(i.assign,3)}return this.finishOp(e===124?i.logicalOR:i.logicalAND,2)}return t===61?this.finishOp(i.assign,2):this.finishOp(e===124?i.bitwiseOR:i.bitwiseAND,1)},G.readToken_caret=function(){var e=this.input.charCodeAt(this.pos+1);return e===61?this.finishOp(i.assign,2):this.finishOp(i.bitwiseXOR,1)},G.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?t===45&&!this.inModule&&this.input.charCodeAt(this.pos+2)===62&&(this.lastTokEnd===0||_.test(this.input.slice(this.lastTokEnd,this.pos)))?(this.skipLineComment(3),this.skipSpace(),this.nextToken()):this.finishOp(i.incDec,2):t===61?this.finishOp(i.assign,2):this.finishOp(i.plusMin,1)},G.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),r=1;return t===e?(r=e===62&&this.input.charCodeAt(this.pos+2)===62?3:2,this.input.charCodeAt(this.pos+r)===61?this.finishOp(i.assign,r+1):this.finishOp(i.bitShift,r)):t===33&&e===60&&!this.inModule&&this.input.charCodeAt(this.pos+2)===45&&this.input.charCodeAt(this.pos+3)===45?(this.skipLineComment(4),this.skipSpace(),this.nextToken()):(t===61&&(r=2),this.finishOp(i.relational,r))},G.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return t===61?this.finishOp(i.equality,this.input.charCodeAt(this.pos+2)===61?3:2):e===61&&t===62&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(i.arrow)):this.finishOp(e===61?i.eq:i.prefix,1)},G.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(t===46){var r=this.input.charCodeAt(this.pos+2);if(r<48||r>57)return this.finishOp(i.questionDot,2)}if(t===63){if(e>=12){var s=this.input.charCodeAt(this.pos+2);if(s===61)return this.finishOp(i.assign,3)}return this.finishOp(i.coalesce,2)}}return this.finishOp(i.question,1)},G.readToken_numberSign=function(){var e=this.options.ecmaVersion,t=35;if(e>=13&&(++this.pos,t=this.fullCharCodeAtPos(),v(t,!0)||t===92))return this.finishToken(i.privateId,this.readWord1());this.raise(this.pos,"Unexpected character '"+Ye(t)+"'")},G.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(i.parenL);case 41:return++this.pos,this.finishToken(i.parenR);case 59:return++this.pos,this.finishToken(i.semi);case 44:return++this.pos,this.finishToken(i.comma);case 91:return++this.pos,this.finishToken(i.bracketL);case 93:return++this.pos,this.finishToken(i.bracketR);case 123:return++this.pos,this.finishToken(i.braceL);case 125:return++this.pos,this.finishToken(i.braceR);case 58:return++this.pos,this.finishToken(i.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(i.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(t===120||t===88)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(t===111||t===79)return this.readRadixNumber(8);if(t===98||t===66)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(i.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+Ye(e)+"'")},G.finishOp=function(e,t){var r=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,r)},G.readRegexp=function(){for(var e,t,r=this.pos;;){this.pos>=this.input.length&&this.raise(r,"Unterminated regular expression");var s=this.input.charAt(this.pos);if(_.test(s)&&this.raise(r,"Unterminated regular expression"),e)e=!1;else{if(s==="[")t=!0;else if(s==="]"&&t)t=!1;else if(s==="/"&&!t)break;e=s==="\\"}++this.pos}var a=this.input.slice(r,this.pos);++this.pos;var h=this.pos,c=this.readWord1();this.containsEsc&&this.unexpected(h);var d=this.regexpState||(this.regexpState=new de(this));d.reset(r,a,c),this.validateRegExpFlags(d),this.validateRegExpPattern(d);var x=null;try{x=new RegExp(a,c)}catch{}return this.finishToken(i.regexp,{pattern:a,flags:c,value:x})},G.readInt=function(e,t,r){for(var s=this.options.ecmaVersion>=12&&t===void 0,a=r&&this.input.charCodeAt(this.pos)===48,h=this.pos,c=0,d=0,x=0,U=t==null?1/0:t;x<U;++x,++this.pos){var W=this.input.charCodeAt(this.pos),re=void 0;if(s&&W===95){a&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),d===95&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),x===0&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),d=W;continue}if(W>=97?re=W-97+10:W>=65?re=W-65+10:W>=48&&W<=57?re=W-48:re=1/0,re>=e)break;d=W,c=c*e+re}return s&&d===95&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===h||t!=null&&this.pos-h!==t?null:c};function Ja(e,t){return t?parseInt(e,8):parseFloat(e.replace(/_/g,""))}function Vr(e){return typeof BigInt!="function"?null:BigInt(e.replace(/_/g,""))}G.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var r=this.readInt(e);return r==null&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&this.input.charCodeAt(this.pos)===110?(r=Vr(this.input.slice(t,this.pos)),++this.pos):v(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(i.num,r)},G.readNumber=function(e){var t=this.pos;!e&&this.readInt(10,void 0,!0)===null&&this.raise(t,"Invalid number");var r=this.pos-t>=2&&this.input.charCodeAt(t)===48;r&&this.strict&&this.raise(t,"Invalid number");var s=this.input.charCodeAt(this.pos);if(!r&&!e&&this.options.ecmaVersion>=11&&s===110){var a=Vr(this.input.slice(t,this.pos));return++this.pos,v(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(i.num,a)}r&&/[89]/.test(this.input.slice(t,this.pos))&&(r=!1),s===46&&!r&&(++this.pos,this.readInt(10),s=this.input.charCodeAt(this.pos)),(s===69||s===101)&&!r&&(s=this.input.charCodeAt(++this.pos),(s===43||s===45)&&++this.pos,this.readInt(10)===null&&this.raise(t,"Invalid number")),v(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var h=Ja(this.input.slice(t,this.pos),r);return this.finishToken(i.num,h)},G.readCodePoint=function(){var e=this.input.charCodeAt(this.pos),t;if(e===123){this.options.ecmaVersion<6&&this.unexpected();var r=++this.pos;t=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,t>1114111&&this.invalidStringToken(r,"Code point out of bounds")}else t=this.readHexChar(4);return t};function Ye(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode((e>>10)+55296,(e&1023)+56320))}G.readString=function(e){for(var t="",r=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var s=this.input.charCodeAt(this.pos);if(s===e)break;s===92?(t+=this.input.slice(r,this.pos),t+=this.readEscapedChar(!1),r=this.pos):s===8232||s===8233?(this.options.ecmaVersion<10&&this.raise(this.start,"Unterminated string constant"),++this.pos,this.options.locations&&(this.curLine++,this.lineStart=this.pos)):(M(s)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(r,this.pos++),this.finishToken(i.string,t)};var Rr={};G.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e===Rr)this.readInvalidTemplateToken();else throw e}this.inTemplateElement=!1},G.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw Rr;this.raise(e,t)},G.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var r=this.input.charCodeAt(this.pos);if(r===96||r===36&&this.input.charCodeAt(this.pos+1)===123)return this.pos===this.start&&(this.type===i.template||this.type===i.invalidTemplate)?r===36?(this.pos+=2,this.finishToken(i.dollarBraceL)):(++this.pos,this.finishToken(i.backQuote)):(e+=this.input.slice(t,this.pos),this.finishToken(i.template,e));if(r===92)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(M(r)){switch(e+=this.input.slice(t,this.pos),++this.pos,r){case 13:this.input.charCodeAt(this.pos)===10&&++this.pos;case 10:e+=`
`;break;default:e+=String.fromCharCode(r);break}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},G.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if(this.input[this.pos+1]!=="{")break;case"`":return this.finishToken(i.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},G.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return`
`;case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return Ye(this.readCodePoint());case 116:return"	";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:this.input.charCodeAt(this.pos)===10&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(this.strict&&this.invalidStringToken(this.pos-1,"Invalid escape sequence"),e){var r=this.pos-1;return this.invalidStringToken(r,"Invalid escape sequence in template string"),null}default:if(t>=48&&t<=55){var s=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],a=parseInt(s,8);return a>255&&(s=s.slice(0,-1),a=parseInt(s,8)),this.pos+=s.length-1,t=this.input.charCodeAt(this.pos),(s!=="0"||t===56||t===57)&&(this.strict||e)&&this.invalidStringToken(this.pos-1-s.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(a)}return M(t)?"":String.fromCharCode(t)}},G.readHexChar=function(e){var t=this.pos,r=this.readInt(16,e);return r===null&&this.invalidStringToken(t,"Bad character escape sequence"),r},G.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,r=this.pos,s=this.options.ecmaVersion>=6;this.pos<this.input.length;){var a=this.fullCharCodeAtPos();if(q(a,s))this.pos+=a<=65535?1:2;else if(a===92){this.containsEsc=!0,e+=this.input.slice(r,this.pos);var h=this.pos;this.input.charCodeAt(++this.pos)!==117&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var c=this.readCodePoint();(t?v:q)(c,s)||this.invalidStringToken(h,"Invalid Unicode escape"),e+=Ye(c),r=this.pos}else break;t=!1}return e+this.input.slice(r,this.pos)},G.readWord=function(){var e=this.readWord1(),t=i.name;return this.keywords.test(e)&&(t=H[e]),this.finishToken(t,e)};var jr="8.7.0";Z.acorn={Parser:Z,version:jr,defaultOptions:me,Position:K,SourceLocation:X,getLineInfo:oe,Node:Ve,TokenType:B,tokTypes:i,keywordTypes:H,TokContext:ne,tokContexts:Q,isIdentifierChar:q,isIdentifierStart:v,Token:Je,isNewLine:M,lineBreak:_,lineBreakG:P,nonASCIIwhitespace:ae};function Ya(e,t){return Z.parse(e,t)}function Qa(e,t,r){return Z.parseExpressionAt(e,t,r)}function Za(e,t){return Z.tokenizer(e,t)}u.Node=Ve,u.Parser=Z,u.Position=K,u.SourceLocation=X,u.TokContext=ne,u.Token=Je,u.TokenType=B,u.defaultOptions=me,u.getLineInfo=oe,u.isIdentifierChar=q,u.isIdentifierStart=v,u.isNewLine=M,u.keywordTypes=H,u.lineBreak=_,u.lineBreakG=P,u.nonASCIIwhitespace=ae,u.parse=Ya,u.parseExpressionAt=Qa,u.tokContexts=Q,u.tokTypes=i,u.tokenizer=Za,u.version=jr,Object.defineProperty(u,"__esModule",{value:!0})})}}),bh=Y({"node_modules/acorn-jsx/xhtml.js"(n,o){J(),o.exports={quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:"\xA0",iexcl:"\xA1",cent:"\xA2",pound:"\xA3",curren:"\xA4",yen:"\xA5",brvbar:"\xA6",sect:"\xA7",uml:"\xA8",copy:"\xA9",ordf:"\xAA",laquo:"\xAB",not:"\xAC",shy:"\xAD",reg:"\xAE",macr:"\xAF",deg:"\xB0",plusmn:"\xB1",sup2:"\xB2",sup3:"\xB3",acute:"\xB4",micro:"\xB5",para:"\xB6",middot:"\xB7",cedil:"\xB8",sup1:"\xB9",ordm:"\xBA",raquo:"\xBB",frac14:"\xBC",frac12:"\xBD",frac34:"\xBE",iquest:"\xBF",Agrave:"\xC0",Aacute:"\xC1",Acirc:"\xC2",Atilde:"\xC3",Auml:"\xC4",Aring:"\xC5",AElig:"\xC6",Ccedil:"\xC7",Egrave:"\xC8",Eacute:"\xC9",Ecirc:"\xCA",Euml:"\xCB",Igrave:"\xCC",Iacute:"\xCD",Icirc:"\xCE",Iuml:"\xCF",ETH:"\xD0",Ntilde:"\xD1",Ograve:"\xD2",Oacute:"\xD3",Ocirc:"\xD4",Otilde:"\xD5",Ouml:"\xD6",times:"\xD7",Oslash:"\xD8",Ugrave:"\xD9",Uacute:"\xDA",Ucirc:"\xDB",Uuml:"\xDC",Yacute:"\xDD",THORN:"\xDE",szlig:"\xDF",agrave:"\xE0",aacute:"\xE1",acirc:"\xE2",atilde:"\xE3",auml:"\xE4",aring:"\xE5",aelig:"\xE6",ccedil:"\xE7",egrave:"\xE8",eacute:"\xE9",ecirc:"\xEA",euml:"\xEB",igrave:"\xEC",iacute:"\xED",icirc:"\xEE",iuml:"\xEF",eth:"\xF0",ntilde:"\xF1",ograve:"\xF2",oacute:"\xF3",ocirc:"\xF4",otilde:"\xF5",ouml:"\xF6",divide:"\xF7",oslash:"\xF8",ugrave:"\xF9",uacute:"\xFA",ucirc:"\xFB",uuml:"\xFC",yacute:"\xFD",thorn:"\xFE",yuml:"\xFF",OElig:"\u0152",oelig:"\u0153",Scaron:"\u0160",scaron:"\u0161",Yuml:"\u0178",fnof:"\u0192",circ:"\u02C6",tilde:"\u02DC",Alpha:"\u0391",Beta:"\u0392",Gamma:"\u0393",Delta:"\u0394",Epsilon:"\u0395",Zeta:"\u0396",Eta:"\u0397",Theta:"\u0398",Iota:"\u0399",Kappa:"\u039A",Lambda:"\u039B",Mu:"\u039C",Nu:"\u039D",Xi:"\u039E",Omicron:"\u039F",Pi:"\u03A0",Rho:"\u03A1",Sigma:"\u03A3",Tau:"\u03A4",Upsilon:"\u03A5",Phi:"\u03A6",Chi:"\u03A7",Psi:"\u03A8",Omega:"\u03A9",alpha:"\u03B1",beta:"\u03B2",gamma:"\u03B3",delta:"\u03B4",epsilon:"\u03B5",zeta:"\u03B6",eta:"\u03B7",theta:"\u03B8",iota:"\u03B9",kappa:"\u03BA",lambda:"\u03BB",mu:"\u03BC",nu:"\u03BD",xi:"\u03BE",omicron:"\u03BF",pi:"\u03C0",rho:"\u03C1",sigmaf:"\u03C2",sigma:"\u03C3",tau:"\u03C4",upsilon:"\u03C5",phi:"\u03C6",chi:"\u03C7",psi:"\u03C8",omega:"\u03C9",thetasym:"\u03D1",upsih:"\u03D2",piv:"\u03D6",ensp:"\u2002",emsp:"\u2003",thinsp:"\u2009",zwnj:"\u200C",zwj:"\u200D",lrm:"\u200E",rlm:"\u200F",ndash:"\u2013",mdash:"\u2014",lsquo:"\u2018",rsquo:"\u2019",sbquo:"\u201A",ldquo:"\u201C",rdquo:"\u201D",bdquo:"\u201E",dagger:"\u2020",Dagger:"\u2021",bull:"\u2022",hellip:"\u2026",permil:"\u2030",prime:"\u2032",Prime:"\u2033",lsaquo:"\u2039",rsaquo:"\u203A",oline:"\u203E",frasl:"\u2044",euro:"\u20AC",image:"\u2111",weierp:"\u2118",real:"\u211C",trade:"\u2122",alefsym:"\u2135",larr:"\u2190",uarr:"\u2191",rarr:"\u2192",darr:"\u2193",harr:"\u2194",crarr:"\u21B5",lArr:"\u21D0",uArr:"\u21D1",rArr:"\u21D2",dArr:"\u21D3",hArr:"\u21D4",forall:"\u2200",part:"\u2202",exist:"\u2203",empty:"\u2205",nabla:"\u2207",isin:"\u2208",notin:"\u2209",ni:"\u220B",prod:"\u220F",sum:"\u2211",minus:"\u2212",lowast:"\u2217",radic:"\u221A",prop:"\u221D",infin:"\u221E",ang:"\u2220",and:"\u2227",or:"\u2228",cap:"\u2229",cup:"\u222A",int:"\u222B",there4:"\u2234",sim:"\u223C",cong:"\u2245",asymp:"\u2248",ne:"\u2260",equiv:"\u2261",le:"\u2264",ge:"\u2265",sub:"\u2282",sup:"\u2283",nsub:"\u2284",sube:"\u2286",supe:"\u2287",oplus:"\u2295",otimes:"\u2297",perp:"\u22A5",sdot:"\u22C5",lceil:"\u2308",rceil:"\u2309",lfloor:"\u230A",rfloor:"\u230B",lang:"\u2329",rang:"\u232A",loz:"\u25CA",spades:"\u2660",clubs:"\u2663",hearts:"\u2665",diams:"\u2666"}}}),Ia=Y({"node_modules/acorn-jsx/index.js"(n,o){"use strict";J();var u=bh(),l=/^[\da-fA-F]+$/,m=/^\d+$/,E=new WeakMap;function y(g){g=g.Parser.acorn||g;let V=E.get(g);if(!V){let R=g.tokTypes,N=g.TokContext,T=g.TokenType,v=new N("<tag",!1),q=new N("</tag",!1),B=new N("<tag>...</tag>",!0,!0),I={tc_oTag:v,tc_cTag:q,tc_expr:B},w={jsxName:new T("jsxName"),jsxText:new T("jsxText",{beforeExpr:!0}),jsxTagStart:new T("jsxTagStart",{startsExpr:!0}),jsxTagEnd:new T("jsxTagEnd")};w.jsxTagStart.updateContext=function(){this.context.push(B),this.context.push(v),this.exprAllowed=!1},w.jsxTagEnd.updateContext=function(C){let H=this.context.pop();H===v&&C===R.slash||H===q?(this.context.pop(),this.exprAllowed=this.curContext()===B):this.exprAllowed=!0},V={tokContexts:I,tokTypes:w},E.set(g,V)}return V}function A(g){if(!g)return g;if(g.type==="JSXIdentifier")return g.name;if(g.type==="JSXNamespacedName")return g.namespace.name+":"+g.name.name;if(g.type==="JSXMemberExpression")return A(g.object)+"."+A(g.property)}o.exports=function(g){return g=g||{},function(V){return b({allowNamespaces:g.allowNamespaces!==!1,allowNamespacedObjects:!!g.allowNamespacedObjects},V)}},Object.defineProperty(o.exports,"tokTypes",{get:function(){return y(ct()).tokTypes},configurable:!0,enumerable:!0});function b(g,V){let R=V.acorn||ct(),N=y(R),T=R.tokTypes,v=N.tokTypes,q=R.tokContexts,B=N.tokContexts.tc_oTag,I=N.tokContexts.tc_cTag,w=N.tokContexts.tc_expr,C=R.isNewLine,H=R.isIdentifierStart,L=R.isIdentifierChar;return class extends V{static get acornJsx(){return N}jsx_readToken(){let i="",_=this.pos;for(;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated JSX contents");let P=this.input.charCodeAt(this.pos);switch(P){case 60:case 123:return this.pos===this.start?P===60&&this.exprAllowed?(++this.pos,this.finishToken(v.jsxTagStart)):this.getTokenFromCode(P):(i+=this.input.slice(_,this.pos),this.finishToken(v.jsxText,i));case 38:i+=this.input.slice(_,this.pos),i+=this.jsx_readEntity(),_=this.pos;break;case 62:case 125:this.raise(this.pos,"Unexpected token `"+this.input[this.pos]+"`. Did you mean `"+(P===62?"&gt;":"&rbrace;")+'` or `{"'+this.input[this.pos]+'"}`?');default:C(P)?(i+=this.input.slice(_,this.pos),i+=this.jsx_readNewLine(!0),_=this.pos):++this.pos}}}jsx_readNewLine(i){let _=this.input.charCodeAt(this.pos),P;return++this.pos,_===13&&this.input.charCodeAt(this.pos)===10?(++this.pos,P=i?`
`:`\r
`):P=String.fromCharCode(_),this.options.locations&&(++this.curLine,this.lineStart=this.pos),P}jsx_readString(i){let _="",P=++this.pos;for(;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");let M=this.input.charCodeAt(this.pos);if(M===i)break;M===38?(_+=this.input.slice(P,this.pos),_+=this.jsx_readEntity(),P=this.pos):C(M)?(_+=this.input.slice(P,this.pos),_+=this.jsx_readNewLine(!1),P=this.pos):++this.pos}return _+=this.input.slice(P,this.pos++),this.finishToken(T.string,_)}jsx_readEntity(){let i="",_=0,P,M=this.input[this.pos];M!=="&"&&this.raise(this.pos,"Entity must start with an ampersand");let ee=++this.pos;for(;this.pos<this.input.length&&_++<10;){if(M=this.input[this.pos++],M===";"){i[0]==="#"?i[1]==="x"?(i=i.substr(2),l.test(i)&&(P=String.fromCharCode(parseInt(i,16)))):(i=i.substr(1),m.test(i)&&(P=String.fromCharCode(parseInt(i,10)))):P=u[i];break}i+=M}return P||(this.pos=ee,"&")}jsx_readWord(){let i,_=this.pos;do i=this.input.charCodeAt(++this.pos);while(L(i)||i===45);return this.finishToken(v.jsxName,this.input.slice(_,this.pos))}jsx_parseIdentifier(){let i=this.startNode();return this.type===v.jsxName?i.name=this.value:this.type.keyword?i.name=this.type.keyword:this.unexpected(),this.next(),this.finishNode(i,"JSXIdentifier")}jsx_parseNamespacedName(){let i=this.start,_=this.startLoc,P=this.jsx_parseIdentifier();if(!g.allowNamespaces||!this.eat(T.colon))return P;var M=this.startNodeAt(i,_);return M.namespace=P,M.name=this.jsx_parseIdentifier(),this.finishNode(M,"JSXNamespacedName")}jsx_parseElementName(){if(this.type===v.jsxTagEnd)return"";let i=this.start,_=this.startLoc,P=this.jsx_parseNamespacedName();for(this.type===T.dot&&P.type==="JSXNamespacedName"&&!g.allowNamespacedObjects&&this.unexpected();this.eat(T.dot);){let M=this.startNodeAt(i,_);M.object=P,M.property=this.jsx_parseIdentifier(),P=this.finishNode(M,"JSXMemberExpression")}return P}jsx_parseAttributeValue(){switch(this.type){case T.braceL:let i=this.jsx_parseExpressionContainer();return i.expression.type==="JSXEmptyExpression"&&this.raise(i.start,"JSX attributes must only be assigned a non-empty expression"),i;case v.jsxTagStart:case T.string:return this.parseExprAtom();default:this.raise(this.start,"JSX value should be either an expression or a quoted JSX text")}}jsx_parseEmptyExpression(){let i=this.startNodeAt(this.lastTokEnd,this.lastTokEndLoc);return this.finishNodeAt(i,"JSXEmptyExpression",this.start,this.startLoc)}jsx_parseExpressionContainer(){let i=this.startNode();return this.next(),i.expression=this.type===T.braceR?this.jsx_parseEmptyExpression():this.parseExpression(),this.expect(T.braceR),this.finishNode(i,"JSXExpressionContainer")}jsx_parseAttribute(){let i=this.startNode();return this.eat(T.braceL)?(this.expect(T.ellipsis),i.argument=this.parseMaybeAssign(),this.expect(T.braceR),this.finishNode(i,"JSXSpreadAttribute")):(i.name=this.jsx_parseNamespacedName(),i.value=this.eat(T.eq)?this.jsx_parseAttributeValue():null,this.finishNode(i,"JSXAttribute"))}jsx_parseOpeningElementAt(i,_){let P=this.startNodeAt(i,_);P.attributes=[];let M=this.jsx_parseElementName();for(M&&(P.name=M);this.type!==T.slash&&this.type!==v.jsxTagEnd;)P.attributes.push(this.jsx_parseAttribute());return P.selfClosing=this.eat(T.slash),this.expect(v.jsxTagEnd),this.finishNode(P,M?"JSXOpeningElement":"JSXOpeningFragment")}jsx_parseClosingElementAt(i,_){let P=this.startNodeAt(i,_),M=this.jsx_parseElementName();return M&&(P.name=M),this.expect(v.jsxTagEnd),this.finishNode(P,M?"JSXClosingElement":"JSXClosingFragment")}jsx_parseElementAt(i,_){let P=this.startNodeAt(i,_),M=[],ee=this.jsx_parseOpeningElementAt(i,_),ae=null;if(!ee.selfClosing){e:for(;;)switch(this.type){case v.jsxTagStart:if(i=this.start,_=this.startLoc,this.next(),this.eat(T.slash)){ae=this.jsx_parseClosingElementAt(i,_);break e}M.push(this.jsx_parseElementAt(i,_));break;case v.jsxText:M.push(this.parseExprAtom());break;case T.braceL:M.push(this.jsx_parseExpressionContainer());break;default:this.unexpected()}A(ae.name)!==A(ee.name)&&this.raise(ae.start,"Expected corresponding JSX closing tag for <"+A(ee.name)+">")}let te=ee.name?"Element":"Fragment";return P["opening"+te]=ee,P["closing"+te]=ae,P.children=M,this.type===T.relational&&this.value==="<"&&this.raise(this.start,"Adjacent JSX elements must be wrapped in an enclosing tag"),this.finishNode(P,"JSX"+te)}jsx_parseText(){let i=this.parseLiteral(this.value);return i.type="JSXText",i}jsx_parseElement(){let i=this.start,_=this.startLoc;return this.next(),this.jsx_parseElementAt(i,_)}parseExprAtom(i){return this.type===v.jsxText?this.jsx_parseText():this.type===v.jsxTagStart?this.jsx_parseElement():super.parseExprAtom(i)}readToken(i){let _=this.curContext();if(_===w)return this.jsx_readToken();if(_===B||_===I){if(H(i))return this.jsx_readWord();if(i==62)return++this.pos,this.finishToken(v.jsxTagEnd);if((i===34||i===39)&&_==B)return this.jsx_readString(i)}return i===60&&this.exprAllowed&&this.input.charCodeAt(this.pos+1)!==33?(++this.pos,this.finishToken(v.jsxTagStart)):super.readToken(i)}updateContext(i){if(this.type==T.braceL){var _=this.curContext();_==B?this.context.push(q.b_expr):_==w?this.context.push(q.b_tmpl):super.updateContext(i),this.exprAllowed=!0}else if(this.type===T.slash&&i===v.jsxTagStart)this.context.length-=2,this.context.push(I),this.exprAllowed=!1;else return super.updateContext(i)}}}}}),_h=Y({"src/language-js/parse/acorn.js"(n,o){"use strict";J();var u=lr(),l=pa(),m=ka(),E=Ta(),y={ecmaVersion:"latest",sourceType:"module",allowReserved:!0,allowReturnOutsideFunction:!0,allowImportExportEverywhere:!0,allowAwaitOutsideFunction:!0,allowSuperOutsideMethod:!0,allowHashBang:!0,locations:!0,ranges:!0};function A(N){let{message:T,loc:v}=N;if(!v)return N;let{line:q,column:B}=v;return u(T.replace(/ \(\d+:\d+\)$/,""),{start:{line:q,column:B+1}})}var b,g=()=>{if(!b){let{Parser:N}=ct(),T=Ia();b=N.extend(T())}return b};function V(N,T){let v=g(),q=[],B=[],I=v.parse(N,Object.assign(Object.assign({},y),{},{sourceType:T,onComment:q,onToken:B}));return I.comments=q,I.tokens=B,I}function R(N,T){let v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},{result:q,error:B}=l(()=>V(N,"module"),()=>V(N,"script"));if(!q)throw A(B);return v.originalText=N,E(q,v)}o.exports=m(R)}}),Sh=Y({"src/language-js/parse/utils/replace-hashbang.js"(n,o){"use strict";J();function u(l){return l.charAt(0)==="#"&&l.charAt(1)==="!"?"//"+l.slice(2):l}o.exports=u}}),wh=Y({"node_modules/espree/dist/espree.cjs"(n){"use strict";J(),Object.defineProperty(n,"__esModule",{value:!0});var o=ct(),u=Ia(),l;function m(p){return p&&typeof p=="object"&&"default"in p?p:{default:p}}function E(p){if(p&&p.__esModule)return p;var D=Object.create(null);return p&&Object.keys(p).forEach(function(S){if(S!=="default"){var f=Object.getOwnPropertyDescriptor(p,S);Object.defineProperty(D,S,f.get?f:{enumerable:!0,get:function(){return p[S]}})}}),D.default=p,Object.freeze(D)}var y=E(o),A=m(u),b=E(l),g={Boolean:"Boolean",EOF:"<end>",Identifier:"Identifier",PrivateIdentifier:"PrivateIdentifier",Keyword:"Keyword",Null:"Null",Numeric:"Numeric",Punctuator:"Punctuator",String:"String",RegularExpression:"RegularExpression",Template:"Template",JSXIdentifier:"JSXIdentifier",JSXText:"JSXText"};function V(p,D){let S=p[0],f=p[p.length-1],F={type:g.Template,value:D.slice(S.start,f.end)};return S.loc&&(F.loc={start:S.loc.start,end:f.loc.end}),S.range&&(F.start=S.range[0],F.end=f.range[1],F.range=[F.start,F.end]),F}function R(p,D){this._acornTokTypes=p,this._tokens=[],this._curlyBrace=null,this._code=D}R.prototype={constructor:R,translate(p,D){let S=p.type,f=this._acornTokTypes;if(S===f.name)p.type=g.Identifier,p.value==="static"&&(p.type=g.Keyword),D.ecmaVersion>5&&(p.value==="yield"||p.value==="let")&&(p.type=g.Keyword);else if(S===f.privateId)p.type=g.PrivateIdentifier;else if(S===f.semi||S===f.comma||S===f.parenL||S===f.parenR||S===f.braceL||S===f.braceR||S===f.dot||S===f.bracketL||S===f.colon||S===f.question||S===f.bracketR||S===f.ellipsis||S===f.arrow||S===f.jsxTagStart||S===f.incDec||S===f.starstar||S===f.jsxTagEnd||S===f.prefix||S===f.questionDot||S.binop&&!S.keyword||S.isAssign)p.type=g.Punctuator,p.value=this._code.slice(p.start,p.end);else if(S===f.jsxName)p.type=g.JSXIdentifier;else if(S.label==="jsxText"||S===f.jsxAttrValueToken)p.type=g.JSXText;else if(S.keyword)S.keyword==="true"||S.keyword==="false"?p.type=g.Boolean:S.keyword==="null"?p.type=g.Null:p.type=g.Keyword;else if(S===f.num)p.type=g.Numeric,p.value=this._code.slice(p.start,p.end);else if(S===f.string)D.jsxAttrValueToken?(D.jsxAttrValueToken=!1,p.type=g.JSXText):p.type=g.String,p.value=this._code.slice(p.start,p.end);else if(S===f.regexp){p.type=g.RegularExpression;let F=p.value;p.regex={flags:F.flags,pattern:F.pattern},p.value="/".concat(F.pattern,"/").concat(F.flags)}return p},onToken(p,D){let S=this,f=this._acornTokTypes,F=D.tokens,K=this._tokens;function X(){F.push(V(S._tokens,S._code)),S._tokens=[]}if(p.type===f.eof){this._curlyBrace&&F.push(this.translate(this._curlyBrace,D));return}if(p.type===f.backQuote){this._curlyBrace&&(F.push(this.translate(this._curlyBrace,D)),this._curlyBrace=null),K.push(p),K.length>1&&X();return}if(p.type===f.dollarBraceL){K.push(p),X();return}if(p.type===f.braceR){this._curlyBrace&&F.push(this.translate(this._curlyBrace,D)),this._curlyBrace=p;return}if(p.type===f.template||p.type===f.invalidTemplate){this._curlyBrace&&(K.push(this._curlyBrace),this._curlyBrace=null),K.push(p);return}this._curlyBrace&&(F.push(this.translate(this._curlyBrace,D)),this._curlyBrace=null),F.push(this.translate(p,D))}};var N=[3,5,6,7,8,9,10,11,12,13];function T(){return N[N.length-1]}function v(){return[...N]}function q(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:5,D=p==="latest"?T():p;if(typeof D!="number")throw new Error('ecmaVersion must be a number or "latest". Received value of type '.concat(typeof p," instead."));if(D>=2015&&(D-=2009),!N.includes(D))throw new Error("Invalid ecmaVersion.");return D}function B(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"script";if(p==="script"||p==="module")return p;if(p==="commonjs")return"script";throw new Error("Invalid sourceType.")}function I(p){let D=q(p.ecmaVersion),S=B(p.sourceType),f=p.range===!0,F=p.loc===!0;if(D!==3&&p.allowReserved)throw new Error("`allowReserved` is only supported when ecmaVersion is 3");if(typeof p.allowReserved<"u"&&typeof p.allowReserved!="boolean")throw new Error("`allowReserved`, when present, must be `true` or `false`");let K=D===3?p.allowReserved||"never":!1,X=p.ecmaFeatures||{},oe=p.sourceType==="commonjs"||Boolean(X.globalReturn);if(S==="module"&&D<6)throw new Error("sourceType 'module' is not supported when ecmaVersion < 2015. Consider adding `{ ecmaVersion: 2015 }` to the parser options.");return Object.assign({},p,{ecmaVersion:D,sourceType:S,ranges:f,locations:F,allowReserved:K,allowReturnOutsideFunction:oe})}var w=Symbol("espree's internal state"),C=Symbol("espree's esprimaFinishNode");function H(p,D,S,f,F,K){let X={type:p?"Block":"Line",value:D};return typeof S=="number"&&(X.start=S,X.end=f,X.range=[S,f]),typeof F=="object"&&(X.loc={start:F,end:K}),X}var L=()=>p=>{let D=Object.assign({},p.acorn.tokTypes);return p.acornJsx&&Object.assign(D,p.acornJsx.tokTypes),class extends p{constructor(f,F){(typeof f!="object"||f===null)&&(f={}),typeof F!="string"&&!(F instanceof String)&&(F=String(F));let K=f.sourceType,X=I(f),oe=X.ecmaFeatures||{},me=X.tokens===!0?new R(D,F):null;super({ecmaVersion:X.ecmaVersion,sourceType:X.sourceType,ranges:X.ranges,locations:X.locations,allowReserved:X.allowReserved,allowReturnOutsideFunction:X.allowReturnOutsideFunction,onToken:ve=>{me&&me.onToken(ve,this[w]),ve.type!==D.eof&&(this[w].lastToken=ve)},onComment:(ve,pt,ft,Ee,ge,De)=>{if(this[w].comments){let Ue=H(ve,pt,ft,Ee,ge,De);this[w].comments.push(Ue)}}},F),this[w]={originalSourceType:K||X.sourceType,tokens:me?[]:null,comments:X.comment===!0?[]:null,impliedStrict:oe.impliedStrict===!0&&this.options.ecmaVersion>=5,ecmaVersion:this.options.ecmaVersion,jsxAttrValueToken:!1,lastToken:null,templateElements:[]}}tokenize(){do this.next();while(this.type!==D.eof);this.next();let f=this[w],F=f.tokens;return f.comments&&(F.comments=f.comments),F}finishNode(){let f=super.finishNode(...arguments);return this[C](f)}finishNodeAt(){let f=super.finishNodeAt(...arguments);return this[C](f)}parse(){let f=this[w],F=super.parse();if(F.sourceType=f.originalSourceType,f.comments&&(F.comments=f.comments),f.tokens&&(F.tokens=f.tokens),F.body.length){let[K]=F.body;F.range&&(F.range[0]=K.range[0]),F.loc&&(F.loc.start=K.loc.start),F.start=K.start}return f.lastToken&&(F.range&&(F.range[1]=f.lastToken.range[1]),F.loc&&(F.loc.end=f.lastToken.loc.end),F.end=f.lastToken.end),this[w].templateElements.forEach(K=>{let oe=K.tail?1:2;K.start+=-1,K.end+=oe,K.range&&(K.range[0]+=-1,K.range[1]+=oe),K.loc&&(K.loc.start.column+=-1,K.loc.end.column+=oe)}),F}parseTopLevel(f){return this[w].impliedStrict&&(this.strict=!0),super.parseTopLevel(f)}raise(f,F){let K=p.acorn.getLineInfo(this.input,f),X=new SyntaxError(F);throw X.index=f,X.lineNumber=K.line,X.column=K.column+1,X}raiseRecoverable(f,F){this.raise(f,F)}unexpected(f){let F="Unexpected token";if(f!=null){if(this.pos=f,this.options.locations)for(;this.pos<this.lineStart;)this.lineStart=this.input.lastIndexOf(`
`,this.lineStart-2)+1,--this.curLine;this.nextToken()}this.end>this.start&&(F+=" ".concat(this.input.slice(this.start,this.end))),this.raise(this.start,F)}jsx_readString(f){let F=super.jsx_readString(f);return this.type===D.string&&(this[w].jsxAttrValueToken=!0),F}[C](f){return f.type==="TemplateElement"&&this[w].templateElements.push(f),f.type.includes("Function")&&!f.generator&&(f.generator=!1),f}}},i="9.3.1",_={_regular:null,_jsx:null,get regular(){return this._regular===null&&(this._regular=y.Parser.extend(L())),this._regular},get jsx(){return this._jsx===null&&(this._jsx=y.Parser.extend(A.default(),L())),this._jsx},get(p){return Boolean(p&&p.ecmaFeatures&&p.ecmaFeatures.jsx)?this.jsx:this.regular}};function P(p,D){let S=_.get(D);return(!D||D.tokens!==!0)&&(D=Object.assign({},D,{tokens:!0})),new S(D,p).tokenize()}function M(p,D){let S=_.get(D);return new S(D,p).parse()}var ee=i,ae=function(){return b.KEYS}(),te=void 0,ie=T(),Ne=v();n.Syntax=te,n.VisitorKeys=ae,n.latestEcmaVersion=ie,n.parse=M,n.supportedEcmaVersions=Ne,n.tokenize=P,n.version=ee}}),kh=Y({"src/language-js/parse/espree.js"(n,o){"use strict";J();var u=lr(),l=pa(),m=ka(),E=Sh(),y=Ta(),A={ecmaVersion:"latest",range:!0,loc:!0,comment:!0,tokens:!0,sourceType:"module",ecmaFeatures:{jsx:!0,globalReturn:!0,impliedStrict:!1}};function b(V){let{message:R,lineNumber:N,column:T}=V;return typeof N!="number"?V:u(R,{start:{line:N,column:T}})}function g(V,R){let N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},{parse:T}=wh(),v=E(V),{result:q,error:B}=l(()=>T(v,Object.assign(Object.assign({},A),{},{sourceType:"module"})),()=>T(v,Object.assign(Object.assign({},A),{},{sourceType:"script"})));if(!q)throw b(B);return N.originalText=V,y(q,N)}o.exports=m(g)}}),Fh=Y({"src/language-js/parse/acorn-and-espree.js"(n,o){J();var u=_h(),l=kh();o.exports={parsers:{acorn:u,espree:l}}}}),Ul=Fh();export{Ul as default};
