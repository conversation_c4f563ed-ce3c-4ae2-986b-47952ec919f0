{"version": 3, "file": "ChromeLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/ChromeLauncher.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAE7C,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAMjD,OAAO,EACL,wBAAwB,EAExB,qBAAqB,GACtB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAC,MAAM,EAAC,MAAM,WAAW,CAAC;AAEjC;;GAEG;AACH,MAAM,OAAO,cAAc;IAczB,YACE,WAA+B,EAC/B,iBAAyB,EACzB,eAAwB;QAExB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,UAAsC,EAAE;QACnD,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,IAAI,GAAG,EAAE,EACT,MAAM,GAAG,KAAK,EACd,OAAO,EACP,cAAc,EACd,IAAI,GAAG,KAAK,EACZ,GAAG,GAAG,OAAO,CAAC,GAAG,EACjB,YAAY,GAAG,IAAI,EACnB,aAAa,GAAG,IAAI,EACpB,YAAY,GAAG,IAAI,EACnB,iBAAiB,GAAG,KAAK,EACzB,eAAe,GAAG,EAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,EAC3C,MAAM,GAAG,CAAC,EACV,OAAO,GAAG,KAAK,EACf,kBAAkB,GAAG,IAAI,EACzB,aAAa,GACd,GAAG,OAAO,CAAC;QAEZ,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,EAAE;YACtB,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;SACpD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YAC3C,eAAe,CAAC,IAAI,CAClB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CACH,CAAC;SACH;aAAM;YACL,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAC/B;QAED,IACE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC/B,OAAO,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,EACF;YACA,IAAI,IAAI,EAAE;gBACR,MAAM,CACJ,CAAC,aAAa,EACd,2EAA2E,CAC5E,CAAC;gBACF,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;aACjD;iBAAM;gBACL,eAAe,CAAC,IAAI,CAAC,2BAA2B,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;aACvE;SACF;QAED,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAE9B,sEAAsE;QACtE,gEAAgE;QAChE,IAAI,gBAAgB,GAAG,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACrD,OAAO,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,gBAAgB,GAAG,CAAC,EAAE;YACxB,iBAAiB,GAAG,IAAI,CAAC;YACzB,eAAe,CAAC,IAAI,CAClB,mBAAmB,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAC1C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,+BAA+B,CAAC,CACrD,EAAE,CACJ,CAAC;YACF,gBAAgB,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;SAC/C;QAED,MAAM,WAAW,GAAG,eAAe,CAAC,gBAAgB,CAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,MAAM,CAAC,OAAO,WAAW,KAAK,QAAQ,EAAE,gCAAgC,CAAC,CAAC;QAE1E,IAAI,gBAAgB,GAAG,cAAc,CAAC;QACtC,IAAI,OAAO,EAAE;YACX,gFAAgF;YAChF,MAAM,CACJ,CAAC,gBAAgB,EACjB,iEAAiE,CAClE,CAAC;YAEF,gBAAgB,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;SACtD;aAAM,IAAI,CAAC,gBAAgB,EAAE;YAC5B,MAAM,EAAC,WAAW,EAAE,cAAc,EAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,WAAW,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;aAC9B;YACD,gBAAgB,GAAG,cAAc,CAAC;SACnC;QAED,MAAM,OAAO,GAAG,eAAe,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;QACpE,MAAM,MAAM,GAAG,IAAI,aAAa,CAC9B,IAAI,CAAC,OAAO,EACZ,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,KAAK,CAAC;YACX,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,MAAM;YACN,GAAG;YACH,IAAI,EAAE,OAAO;SACd,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC;QACZ,IAAI;YACF,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC;gBAC9C,OAAO;gBACP,OAAO;gBACP,MAAM;gBACN,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;aAC3C,CAAC,CAAC;YACH,OAAO,GAAG,MAAM,OAAO,CAAC,OAAO,CAC7B,IAAI,CAAC,OAAO,EACZ,UAAU,EACV,EAAE,EACF,iBAAiB,EACjB,eAAe,EACf,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EACzB,OAAO,CAAC,YAAY,CACrB,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,KAAK,CAAC;SACb;QAED,IAAI,kBAAkB,EAAE;YACtB,IAAI;gBACF,MAAM,OAAO,CAAC,aAAa,CACzB,CAAC,CAAC,EAAE;oBACF,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;gBAC7B,CAAC,EACD,EAAC,OAAO,EAAC,CACV,CAAC;aACH;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,KAAK,CAAC;aACb;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,WAAW,CAAC,UAAwC,EAAE;QACpD,MAAM,eAAe,GAAG;YACtB,0BAA0B;YAC1B,iCAAiC;YACjC,4CAA4C;YAC5C,uCAAuC;YACvC,0CAA0C;YAC1C,oBAAoB;YACpB,0CAA0C;YAC1C,sDAAsD;YACtD,wBAAwB;YACxB,yBAAyB;YACzB,sBAAsB;YACtB,2DAA2D;YAC3D,gDAAgD;YAChD,uDAAuD;YACvD,mGAAmG;YACnG,wBAAwB;YACxB,mCAAmC;YACnC,0BAA0B;YAC1B,4BAA4B;YAC5B,kCAAkC;YAClC,gBAAgB;YAChB,4BAA4B;YAC5B,0BAA0B;YAC1B,gBAAgB;YAChB,qBAAqB;YACrB,wBAAwB;YACxB,qBAAqB;YACrB,8DAA8D;YAC9D,8CAA8C;YAC9C,uCAAuC;YACvC,qBAAqB;SACtB,CAAC;QACF,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,QAAQ,EACpB,IAAI,GAAG,EAAE,EACT,WAAW,GACZ,GAAG,OAAO,CAAC;QACZ,IAAI,WAAW,EAAE;YACf,eAAe,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;SACtE;QACD,IAAI,QAAQ,EAAE;YACZ,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;SACvD;QACD,IAAI,QAAQ,EAAE;YACZ,eAAe,CAAC,IAAI,CAClB,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,YAAY,EAC1D,mBAAmB,EACnB,cAAc,CACf,CAAC;SACH;QACD,IACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,EACF;YACA,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACrC;QACD,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC9B,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,cAAc,CAAC,OAA8B;QAC3C,IAAI,OAAO,EAAE;YACX,OAAO,wBAAwB,CAAC,OAAO,CAAC,CAAC;SAC1C;aAAM;YACL,MAAM,OAAO,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC5C,OAAO,OAAO,CAAC,cAAc,CAAC;SAC/B;IACH,CAAC;IAED,IAAI,OAAO;QACT,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF"}