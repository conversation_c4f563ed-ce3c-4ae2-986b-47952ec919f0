{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../src/common/Browser.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAIH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAyB,uBAAuB,EAAC,MAAM,iBAAiB,CAAC;AAChF,OAAO,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAC,eAAe,EAAC,MAAM,WAAW,CAAC;AAG1C,OAAO,EAAC,MAAM,EAAC,MAAM,aAAa,CAAC;AACnC,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAEzC,OAAO,EAAC,mBAAmB,EAAC,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAC,oBAAoB,EAAC,MAAM,2BAA2B,CAAC;AAsC/D,MAAM,qCAAqC,GAAG,IAAI,GAAG,CAGnD;IACA,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,iCAAiC;IACjC,oBAAoB;IACpB,CAAC,QAAQ,EAAE,cAAc,CAAC;IAC1B,CAAC,YAAY,EAAE,cAAc,CAAC;IAC9B,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,sBAAsB,EAAE,SAAS,CAAC;IACnC,CAAC,eAAe,EAAE,SAAS,CAAC;IAC5B,CAAC,WAAW,EAAE,SAAS,CAAC;IACxB,CAAC,cAAc,EAAE,SAAS,CAAC;IAC3B,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;IAC/C,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;IACxC,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;IACzC,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IACxC,CAAC,gBAAgB,EAAE,eAAe,CAAC;IACnC,uCAAuC;IACvC,CAAC,YAAY,EAAE,WAAW,CAAC;CAC5B,CAAC,CAAC;AAmFH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AACH,MAAM,OAAO,OAAQ,SAAQ,YAAY;IAgDvC;;OAEG;IACH,YACE,OAAyC,EACzC,UAAsB,EACtB,UAAoB,EACpB,iBAA0B,EAC1B,eAAiC,EACjC,OAAsB,EACtB,aAAoC,EACpC,oBAA2C,EAC3C,oBAA2C;QAE3C,KAAK,EAAE,CAAC;;QAjCV,6CAA4B;QAC5B,2CAAmC;QACnC,mCAAwB;QACxB,sCAAwB;QACxB,yCAAqC;QACrC,gDAA4C;QAC5C,gDAA6C;QAC7C,0CAAgC;QAChC,oCAAuC;QACvC,+CAAgC;QAChC,yCAA8B;QA4D9B,oCAAoB,GAAG,EAAE;YACvB,IAAI,CAAC,IAAI,wDAAmC,CAAC;QAC/C,CAAC,EAAC;QA4JF,gCAAgB,CACd,UAAsC,EACtC,OAAoB,EACpB,EAAE;;YACF,MAAM,EAAC,gBAAgB,EAAC,GAAG,UAAU,CAAC;YACtC,MAAM,OAAO,GACX,gBAAgB,IAAI,uBAAA,IAAI,yBAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBACtD,CAAC,CAAC,uBAAA,IAAI,yBAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBACtC,CAAC,CAAC,uBAAA,IAAI,+BAAgB,CAAC;YAE3B,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;aAC5C;YAED,OAAO,IAAI,MAAM,CACf,UAAU,EACV,OAAO,EACP,OAAO,EACP,uBAAA,IAAI,8BAAe,EACnB,CAAC,oBAA6B,EAAE,EAAE;gBAChC,OAAO,uBAAA,IAAI,2BAAY,CAAC,cAAc,CACpC,UAAU,EACV,oBAAoB,CACrB,CAAC;YACJ,CAAC,EACD,uBAAA,IAAI,kCAAmB,EACvB,MAAA,uBAAA,IAAI,gCAAiB,mCAAI,IAAI,EAC7B,uBAAA,IAAI,oCAAqB,EACzB,uBAAA,IAAI,qCAAsB,CAC3B,CAAC;QACJ,CAAC,EAAC;QAEF,sCAAsB,KAAK,EAAE,MAAc,EAAE,EAAE;YAC7C,IAAI,MAAM,MAAM,CAAC,mBAAmB,EAAE;gBACpC,IAAI,CAAC,IAAI,2DAAqC,MAAM,CAAC,CAAC;gBACtD,MAAM;qBACH,cAAc,EAAE;qBAChB,IAAI,kEAA4C,MAAM,CAAC,CAAC;aAC5D;QACH,CAAC,EAAC;QAEF,wCAAwB,KAAK,EAAE,MAAc,EAAiB,EAAE;YAC9D,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,MAAM,MAAM,CAAC,mBAAmB,EAAE;gBACpC,IAAI,CAAC,IAAI,+DAAuC,MAAM,CAAC,CAAC;gBACxD,MAAM;qBACH,cAAc,EAAE;qBAChB,IAAI,sEAA8C,MAAM,CAAC,CAAC;aAC9D;QACH,CAAC,EAAC;QAEF,mCAAmB,CAAC,EAClB,MAAM,EACN,UAAU,GAIX,EAAQ,EAAE;YACT,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YACjC,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YAC7C,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACtC,IAAI,cAAc,IAAI,WAAW,KAAK,MAAM,CAAC,GAAG,EAAE,EAAE;gBAClD,IAAI,CAAC,IAAI,2DAAqC,MAAM,CAAC,CAAC;gBACtD,MAAM;qBACH,cAAc,EAAE;qBAChB,IAAI,kEAA4C,MAAM,CAAC,CAAC;aAC5D;QACH,CAAC,EAAC;QAEF,sCAAsB,CAAC,UAAsC,EAAQ,EAAE;YACrE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAC5C,CAAC,EAAC;QA1QA,OAAO,GAAG,OAAO,IAAI,QAAQ,CAAC;QAC9B,uBAAA,IAAI,8BAAsB,iBAAiB,MAAA,CAAC;QAC5C,uBAAA,IAAI,4BAAoB,eAAe,MAAA,CAAC;QACxC,uBAAA,IAAI,oBAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,gCAAwB,IAAI,SAAS,EAAE,MAAA,CAAC;QAC5C,uBAAA,IAAI,uBAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,0BAAkB,aAAa,IAAI,cAAmB,CAAC,MAAA,CAAC;QAC5D,uBAAA,IAAI,iCACF,oBAAoB;YACpB,CAAC,GAAY,EAAE;gBACb,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,MAAA,CAAC;QACL,uBAAA,IAAI,4DAAyB,MAA7B,IAAI,EAA0B,oBAAoB,CAAC,CAAC;QACpD,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,uBAAA,IAAI,0BAAkB,IAAI,oBAAoB,CAC5C,UAAU,EACV,uBAAA,IAAI,6BAAc,EAClB,uBAAA,IAAI,qCAAsB,CAC3B,MAAA,CAAC;SACH;aAAM;YACL,uBAAA,IAAI,0BAAkB,IAAI,mBAAmB,CAC3C,UAAU,EACV,uBAAA,IAAI,6BAAc,EAClB,uBAAA,IAAI,qCAAsB,CAC3B,MAAA,CAAC;SACH;QACD,uBAAA,IAAI,2BAAmB,IAAI,cAAc,CAAC,uBAAA,IAAI,2BAAY,EAAE,IAAI,CAAC,MAAA,CAAC;QAClE,uBAAA,IAAI,qBAAa,IAAI,GAAG,EAAE,MAAA,CAAC;QAC3B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,uBAAA,IAAI,yBAAU,CAAC,GAAG,CAChB,SAAS,EACT,IAAI,cAAc,CAAC,uBAAA,IAAI,2BAAY,EAAE,IAAI,EAAE,SAAS,CAAC,CACtD,CAAC;SACH;IACH,CAAC;IAhGD;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,OAAyC,EACzC,UAAsB,EACtB,UAAoB,EACpB,iBAA0B,EAC1B,eAAiC,EACjC,OAAsB,EACtB,aAAoC,EACpC,oBAA2C,EAC3C,oBAA2C;QAE3C,MAAM,OAAO,GAAG,IAAI,OAAO,CACzB,OAAO,EACP,UAAU,EACV,UAAU,EACV,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,aAAa,EACb,oBAAoB,EACpB,oBAAoB,CACrB,CAAC;QACF,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,OAAO,OAAO,CAAC;IACjB,CAAC;IAaD;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,uBAAA,IAAI,8BAAe,CAAC,mBAAmB,EAAE,CAAC;IACnD,CAAC;IAyDD;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,uBAAA,IAAI,2BAAY,CAAC,EAAE,CACjB,uBAAuB,CAAC,YAAY,EACpC,uBAAA,IAAI,iCAAkB,CACvB,CAAC;QACF,uBAAA,IAAI,8BAAe,CAAC,EAAE,qEAEpB,uBAAA,IAAI,mCAAoB,CACzB,CAAC;QACF,uBAAA,IAAI,8BAAe,CAAC,EAAE,2DAEpB,uBAAA,IAAI,qCAAsB,CAC3B,CAAC;QACF,uBAAA,IAAI,8BAAe,CAAC,EAAE,iEAEpB,uBAAA,IAAI,gCAAiB,CACtB,CAAC;QACF,uBAAA,IAAI,8BAAe,CAAC,EAAE,uEAEpB,uBAAA,IAAI,mCAAoB,CACzB,CAAC;QACF,MAAM,uBAAA,IAAI,8BAAe,CAAC,UAAU,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,uBAAA,IAAI,2BAAY,CAAC,GAAG,CAClB,uBAAuB,CAAC,YAAY,EACpC,uBAAA,IAAI,iCAAkB,CACvB,CAAC;QACF,uBAAA,IAAI,8BAAe,CAAC,GAAG,qEAErB,uBAAA,IAAI,mCAAoB,CACzB,CAAC;QACF,uBAAA,IAAI,8BAAe,CAAC,GAAG,2DAErB,uBAAA,IAAI,qCAAsB,CAC3B,CAAC;QACF,uBAAA,IAAI,8BAAe,CAAC,GAAG,iEAErB,uBAAA,IAAI,gCAAiB,CACtB,CAAC;QACF,uBAAA,IAAI,8BAAe,CAAC,GAAG,uEAErB,uBAAA,IAAI,mCAAoB,CACzB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,OAAO;;QACL,OAAO,MAAA,uBAAA,IAAI,wBAAS,mCAAI,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,uBAAA,IAAI,8BAAe,CAAC;IAC7B,CAAC;IAcD;;OAEG;IACH,wBAAwB;QACtB,OAAO,uBAAA,IAAI,qCAAsB,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,6BAA6B,CACjC,UAAiC,EAAE;QAEnC,MAAM,EAAC,WAAW,EAAE,eAAe,EAAC,GAAG,OAAO,CAAC;QAE/C,MAAM,EAAC,gBAAgB,EAAC,GAAG,MAAM,uBAAA,IAAI,2BAAY,CAAC,IAAI,CACpD,6BAA6B,EAC7B;YACE,WAAW;YACX,eAAe,EAAE,eAAe,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC;SAC9D,CACF,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,cAAc,CAChC,uBAAA,IAAI,2BAAY,EAChB,IAAI,EACJ,gBAAgB,CACjB,CAAC;QACF,uBAAA,IAAI,yBAAU,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC9C,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,eAAe;QACb,OAAO,CAAC,uBAAA,IAAI,+BAAgB,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,yBAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,uBAAA,IAAI,+BAAgB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,SAAkB;QACtC,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QACD,MAAM,uBAAA,IAAI,2BAAY,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC1D,gBAAgB,EAAE,SAAS;SAC5B,CAAC,CAAC;QACH,uBAAA,IAAI,yBAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IA4ED;;;;;;;;;;;;;;;;OAgBG;IACH,UAAU;QACR,OAAO,uBAAA,IAAI,2BAAY,CAAC,GAAG,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,uBAAA,IAAI,+BAAgB,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,SAAkB;QAC3C,MAAM,EAAC,QAAQ,EAAC,GAAG,MAAM,uBAAA,IAAI,2BAAY,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACpE,GAAG,EAAE,aAAa;YAClB,gBAAgB,EAAE,SAAS,IAAI,SAAS;SACzC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,uBAAA,IAAI,8BAAe,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvE,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,GAAG,CAAC,CAAC;SAC/D;QACD,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC;QACrD,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,0CAA0C,QAAQ,GAAG,CAAC,CAAC;SACxE;QACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CACb,6CAA6C,SAAS,GAAG,CAC1D,CAAC;SACH;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,OAAO;QACL,OAAO,KAAK,CAAC,IAAI,CACf,uBAAA,IAAI,8BAAe,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,CACnD,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAChB,OAAO,MAAM,CAAC,cAAc,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACjD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,KAAK,CAAC,aAAa,CACjB,SAAoD,EACpD,UAAgC,EAAE;QAElC,MAAM,EAAC,OAAO,GAAG,KAAK,EAAC,GAAG,OAAO,CAAC;QAClC,IAAI,OAAsD,CAAC;QAC3D,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,MAAM,aAAa,GAAG,IAAI,OAAO,CAAS,CAAC,CAAC,EAAE;YAC5C,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,EAAE,2DAAqC,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,EAAE,2DAAqC,KAAK,CAAC,CAAC;QACnD,IAAI;YACF,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO,MAAM,aAAa,CAAC;aAC5B;YACD,OAAO,MAAM,eAAe,CAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;SAChE;gBAAS;YACR,IAAI,CAAC,GAAG,2DAAqC,KAAK,CAAC,CAAC;YACpD,IAAI,CAAC,GAAG,2DAAqC,KAAK,CAAC,CAAC;SACrD;QAED,KAAK,UAAU,KAAK,CAAC,MAAc;YACjC,IAAI,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC5C,UAAU,GAAG,IAAI,CAAC;gBAClB,OAAO,CAAC,MAAM,CAAC,CAAC;aACjB;QACH,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CACpC,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACnC,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC,CACH,CAAC;QACF,iBAAiB;QACjB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;YACpC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,OAAO;QACX,MAAM,OAAO,GAAG,MAAM,uBAAA,IAAI,+CAAY,MAAhB,IAAI,CAAc,CAAC;QACzC,OAAO,OAAO,CAAC,OAAO,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAAS;QACb,MAAM,OAAO,GAAG,MAAM,uBAAA,IAAI,+CAAY,MAAhB,IAAI,CAAc,CAAC;QACzC,OAAO,OAAO,CAAC,SAAS,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,uBAAA,IAAI,8BAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,UAAU;QACR,uBAAA,IAAI,8BAAe,CAAC,OAAO,EAAE,CAAC;QAC9B,uBAAA,IAAI,2BAAY,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,CAAC,uBAAA,IAAI,2BAAY,CAAC,OAAO,CAAC;IACnC,CAAC;CAKF;2zBAzW0B,oBAA2C;IAClE,uBAAA,IAAI,iCACF,oBAAoB;QACpB,CAAC,CAAC,MAAkC,EAAW,EAAE;YAC/C,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,MAAM;gBACtB,MAAM,CAAC,IAAI,KAAK,iBAAiB;gBACjC,MAAM,CAAC,IAAI,KAAK,SAAS,CAC1B,CAAC;QACJ,CAAC,CAAC,MAAA,CAAC;AACP,CAAC;IA6VC,OAAO,uBAAA,IAAI,2BAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AACrD,CAAC;AA4BH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,OAAO,cAAe,SAAQ,YAAY;IAK9C;;OAEG;IACH,YAAY,UAAsB,EAAE,OAAgB,EAAE,SAAkB;QACtE,KAAK,EAAE,CAAC;QARV,6CAAwB;QACxB,0CAAkB;QAClB,qCAAa;QAOX,uBAAA,IAAI,8BAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,2BAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,sBAAO,SAAS,MAAA,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,uBAAA,IAAI,+BAAS,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7C,OAAO,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,aAAa,CACX,SAAoD,EACpD,UAA8B,EAAE;QAEhC,OAAO,uBAAA,IAAI,+BAAS,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;YAC1C,OAAO,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,IAAI,CAAC,OAAO,EAAE;aACX,MAAM,CAAC,MAAM,CAAC,EAAE;;YACf,OAAO,CACL,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM;gBACxB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,OAAO;qBACxB,MAAA,uBAAA,IAAI,+BAAS,CAAC,wBAAwB,EAAE,0CACtC,MAAM,CAAC,cAAc,EAAE,CACxB,CAAA,CAAC,CACL,CAAC;QACJ,CAAC,CAAC;aACD,GAAG,CAAC,MAAM,CAAC,EAAE;YACZ,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;QACvB,CAAC,CAAC,CACL,CAAC;QACF,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAgB,EAAE;YACzC,OAAO,CAAC,CAAC,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,WAAW;QACT,OAAO,CAAC,CAAC,uBAAA,IAAI,0BAAI,CAAC;IACpB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,mBAAmB,CACvB,MAAc,EACd,WAAyB;QAEzB,MAAM,mBAAmB,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACvD,MAAM,kBAAkB,GACtB,qCAAqC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,kBAAkB,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,UAAU,CAAC,CAAC;aACtD;YACD,OAAO,kBAAkB,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,MAAM,uBAAA,IAAI,kCAAY,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtD,MAAM;YACN,gBAAgB,EAAE,uBAAA,IAAI,0BAAI,IAAI,SAAS;YACvC,WAAW,EAAE,mBAAmB;SACjC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,wBAAwB;QAC5B,MAAM,uBAAA,IAAI,kCAAY,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtD,gBAAgB,EAAE,uBAAA,IAAI,0BAAI,IAAI,SAAS;SACxC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,uBAAA,IAAI,+BAAS,CAAC,oBAAoB,CAAC,uBAAA,IAAI,0BAAI,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,uBAAA,IAAI,+BAAS,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,CAAC,uBAAA,IAAI,0BAAI,EAAE,0CAA0C,CAAC,CAAC;QAC7D,MAAM,uBAAA,IAAI,+BAAS,CAAC,eAAe,CAAC,uBAAA,IAAI,0BAAI,CAAC,CAAC;IAChD,CAAC;CACF"}