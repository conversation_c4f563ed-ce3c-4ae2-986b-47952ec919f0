{"version": 3, "file": "sizes2.js", "sources": ["../../../../../../../packages/components/pagination/src/components/sizes.vue"], "sourcesContent": ["<template>\n  <span :class=\"ns.e('sizes')\">\n    <el-select\n      :model-value=\"innerPageSize\"\n      :disabled=\"disabled\"\n      :popper-class=\"popperClass\"\n      :size=\"size\"\n      :teleported=\"teleported\"\n      :validate-event=\"false\"\n      :append-to=\"appendSizeTo\"\n      @change=\"handleChange\"\n    >\n      <el-option\n        v-for=\"item in innerPageSizes\"\n        :key=\"item\"\n        :value=\"item\"\n        :label=\"item + t('el.pagination.pagesize')\"\n      />\n    </el-select>\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, watch } from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { ElOption, ElSelect } from '@element-plus/components/select'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\nimport { usePagination } from '../usePagination'\nimport { paginationSizesProps } from './sizes'\n\ndefineOptions({\n  name: 'ElPaginationSizes',\n})\n\nconst props = defineProps(paginationSizesProps)\nconst emit = defineEmits(['page-size-change'])\nconst { t } = useLocale()\nconst ns = useNamespace('pagination')\nconst pagination = usePagination()\nconst innerPageSize = ref<number>(props.pageSize!)\n\nwatch(\n  () => props.pageSizes,\n  (newVal, oldVal) => {\n    if (isEqual(newVal, oldVal)) return\n    if (isArray(newVal)) {\n      const pageSize = newVal.includes(props.pageSize!)\n        ? props.pageSize\n        : props.pageSizes[0]\n      emit('page-size-change', pageSize)\n    }\n  }\n)\n\nwatch(\n  () => props.pageSize,\n  (newVal) => {\n    innerPageSize.value = newVal!\n  }\n)\n\nconst innerPageSizes = computed(() => props.pageSizes)\nfunction handleChange(val: number) {\n  if (val !== innerPageSize.value) {\n    innerPageSize.value = val\n    pagination.handleSizeChange?.(Number(val))\n  }\n}\n</script>\n"], "names": ["useLocale", "useNamespace", "usePagination", "ref", "watch", "isArray", "computed", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;;;;;uCA+Bc,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIA,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,YAAY,CAAA,CAAA;AACpC,IAAA,MAAM,aAAaC,2BAAc,EAAA,CAAA;AACjC,IAAM,MAAA,aAAA,GAAgBC,OAAY,CAAA,KAAA,CAAM,QAAS,CAAA,CAAA;AAEjD,IAAAC,SAAA,CAAA,MAAA,KAAA,CAAA,SAAA,EAAA,CAAA,MAAA,EAAA,MAAA,KAAA;AAAA,MACE,yBAAY,CAAA,MAAA,EAAA,MAAA,CAAA;AAAA,eACH;AACP,MAAI,IAAAC,cAAA,CAAA,MAAgB,CAAA,EAAA;AACpB,QAAI,MAAA,iBAAiB,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,KAAA,CAAA,QAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AACnB,QAAM,IAAA,CAAA,kBAAkB,EAAA,QAAA,CAAA,CAAS;AAGjC,OAAA;AAAiC,KACnC,CAAA,CAAA;AAAA,IACFD,SAAA,CAAA,MAAA,KAAA,CAAA,QAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACF,aAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAA,oBACc,GAAAE,YAAA,CAAA,MAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAAA,IAAA,SACA,YAAA,CAAA,GAAA,EAAA;AACV,MAAA,IAAA,EAAA,CAAA;AAAsB,MACxB,IAAA,GAAA,KAAA,aAAA,CAAA,KAAA,EAAA;AAAA,QACF,aAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAEA,QAAA,CAAA,EAAuB,GAAA,UAAA,CAAA,gBAAe,KAAA,IAAe,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,UAAA,EAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACrD,OAAA;AACE,KAAI;AACF,IAAA,OAAA,CAAA,IAAA,EAAA,MAAsB,KAAA;AACtB,MAAW,OAAAC,aAAA,EAAA,EAAAC,sBAA0B,CAAA,MAAI,EAAA;AAAA,QAC3C,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,OACF,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}