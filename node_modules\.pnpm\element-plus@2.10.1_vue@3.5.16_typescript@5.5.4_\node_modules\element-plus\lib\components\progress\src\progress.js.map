{"version": 3, "file": "progress.js", "sources": ["../../../../../../packages/components/progress/src/progress.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes, SVGAttributes } from 'vue'\nimport type Progress from './progress.vue'\n\nexport type ProgressColor = { color: string; percentage: number }\nexport type ProgressFn = (percentage: number) => string\n\nexport const progressProps = buildProps({\n  /**\n   * @description type of progress bar\n   */\n  type: {\n    type: String,\n    default: 'line',\n    values: ['line', 'circle', 'dashboard'],\n  },\n  /**\n   * @description percentage, required\n   */\n  percentage: {\n    type: Number,\n    default: 0,\n    validator: (val: number): boolean => val >= 0 && val <= 100,\n  },\n  /**\n   * @description the current status of progress bar\n   */\n  status: {\n    type: String,\n    default: '',\n    values: ['', 'success', 'exception', 'warning'],\n  },\n  /**\n   * @description set indeterminate progress\n   */\n  indeterminate: Boolean,\n  /**\n   * @description control the animation duration of indeterminate progress or striped flow progress\n   */\n  duration: {\n    type: Number,\n    default: 3,\n  },\n  /**\n   * @description the width of progress bar\n   */\n  strokeWidth: {\n    type: Number,\n    default: 6,\n  },\n  /**\n   * @description butt/circle/dashboard type shape at the end path\n   */\n  strokeLinecap: {\n    type: definePropType<NonNullable<SVGAttributes['stroke-linecap']>>(String),\n    default: 'round',\n  },\n  /**\n   * @description whether to place the percentage inside progress bar, only works when `type` is 'line'\n   */\n  textInside: Boolean,\n  /**\n   * @description the canvas width of circle progress bar\n   */\n  width: {\n    type: Number,\n    default: 126,\n  },\n  /**\n   * @description whether to show percentage\n   */\n  showText: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description background color of progress bar. Overrides `status` prop\n   */\n  color: {\n    type: definePropType<string | ProgressColor[] | ProgressFn>([\n      String,\n      Array,\n      Function,\n    ]),\n    default: '',\n  },\n  /**\n   * @description stripe over the progress bar's color\n   */\n  striped: Boolean,\n  /**\n   * @description get the stripes to flow\n   */\n  stripedFlow: Boolean,\n  /**\n   * @description custom text format\n   */\n  format: {\n    type: definePropType<ProgressFn>(Function),\n    default: (percentage: number): string => `${percentage}%`,\n  },\n} as const)\n\nexport type ProgressProps = ExtractPropTypes<typeof progressProps>\nexport type ProgressInstance = InstanceType<typeof Progress> & unknown\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,aAAa,GAAGA,kBAAU,CAAC;AACxC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC;AAC3C,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG;AAC9C,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;AACnD,GAAG;AACH,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,WAAW,EAAE,OAAO;AACtB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,CAAC,UAAU,KAAK,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;AAC7C,GAAG;AACH,CAAC;;;;"}