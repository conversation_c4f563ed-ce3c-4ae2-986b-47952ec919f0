{"name": "yargs", "version": "7.1.2", "description": "yargs the modern, pirate-themed, successor to optimist.", "main": "./index.js", "files": ["index.js", "yargs.js", "lib", "locales", "completion.sh.hbs", "LICENSE"], "dependencies": {"camelcase": "^3.0.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.2", "which-module": "^1.0.0", "y18n": "^3.2.1", "yargs-parser": "^5.0.1"}, "devDependencies": {"chai": "^3.4.1", "chalk": "^1.1.3", "coveralls": "^2.11.11", "cpr": "^2.0.0", "cross-spawn": "^5.0.1", "es6-promise": "^4.0.2", "hashish": "0.0.4", "mocha": "^3.0.1", "nyc": "^10.0.0", "rimraf": "^2.5.0", "standard": "^8.6.0", "standard-version": "^3.0.0", "which": "^1.2.9"}, "scripts": {"pretest": "standard", "test": "nyc --cache mocha --require ./test/before.js --timeout=8000 --check-leaks", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "http://github.com/yargs/yargs.git"}, "homepage": "http://yargs.js.org/", "standard": {"ignore": ["**/example/**"]}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "engine": {"node": ">=0.10"}, "greenkeeper": {"ignore": ["string-width", "read-pkg-up", "camelcase"]}}