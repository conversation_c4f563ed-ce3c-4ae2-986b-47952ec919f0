{"version": 3, "file": "IsolatedWorld.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/IsolatedWorld.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAK3C,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAEjD,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AAEjC,OAAO,EAAC,WAAW,EAAC,MAAM,YAAY,CAAC;AACvC,OAAO,EAAC,QAAQ,EAAC,MAAM,eAAe,CAAC;AACvC,OAAO,EAAmB,uBAAuB,EAAC,MAAM,uBAAuB,CAAC;AAEhF,OAAO,EAAC,YAAY,EAAE,SAAS,EAAE,OAAO,EAAC,MAAM,YAAY,CAAC;AAsB5D;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC;;;;;OAKG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,EAAE,QAAQ,CAAC;CACxB;AAED;;;;;GAKG;AACH,eAAO,MAAM,UAAU,eAAsB,CAAC;AAC9C;;;;;GAKG;AACH,eAAO,MAAM,eAAe,eAA2B,CAAC;AACxD;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,CAAC,GAAG,EAAE,MAAM,GAAG,aAAa,CAAC;IAC7B,CAAC,UAAU,CAAC,EAAE,aAAa,CAAC;IAC5B,CAAC,eAAe,CAAC,EAAE,aAAa,CAAC;CAClC;AAED;;GAEG;AACH,qBAAa,aAAa;;IAcxB,IAAI,UAAU,IAAI,GAAG,CAAC,QAAQ,CAAC,CAE9B;IAED,IAAI,eAAe,IAAI,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAE3C;gBAMW,KAAK,EAAE,KAAK,EAAE,QAAQ,UAAQ;IAoB1C,KAAK,IAAI,KAAK;IAId,YAAY,IAAI,IAAI;IAKpB,UAAU,CAAC,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAW3C,UAAU,IAAI,OAAO;IAIrB,OAAO,IAAI,IAAI;IAUf,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAYvC,cAAc,CAClB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAK1C,QAAQ,CACZ,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAK/B,CAAC,CAAC,QAAQ,SAAS,MAAM,EAC7B,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAK7C,EAAE,CAAC,QAAQ,SAAS,MAAM,EAC9B,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAK7C,QAAQ,IAAI,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAW5C,EAAE,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAK3D,KAAK,CACT,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CACvB;QAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAAE,GAAG,MAAM;KAAC,CAC9C,GAAG,YAAY,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,EAE/D,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAK/B,MAAM,CACV,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CACvB;QAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAAE,GAAG,MAAM;KAAC,CACtC,GAAG,YAAY,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,EAEvD,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAK/B,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAa1B,UAAU,CACd,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,IAAI,CAAC;IA4BV,KAAK,CACT,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE;QAAC,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,WAAW,CAAC;QAAC,UAAU,CAAC,EAAE,MAAM,CAAA;KAAC,GACnE,OAAO,CAAC,IAAI,CAAC;IAOV,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAOtC,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAOtC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAQhE,GAAG,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAOpC,IAAI,CACR,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QAAC,KAAK,EAAE,MAAM,CAAA;KAAC,GACxB,OAAO,CAAC,IAAI,CAAC;IAWV,oBAAoB,CACxB,OAAO,EAAE,gBAAgB,EACzB,IAAI,EAAE,MAAM,GACX,OAAO,CAAC,IAAI,CAAC;IA4GV,sBAAsB,CAC1B,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,SAAS,EACrC,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,sBAAsB,EAC/B,OAAO,CAAC,EAAE,WAAW,GACpB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;IAkCpC,eAAe,CACb,YAAY,EAAE,QAAQ,GAAG,MAAM,EAC/B,OAAO,GAAE;QAAC,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAM,EAC3D,GAAG,IAAI,EAAE,OAAO,EAAE,GACjB,OAAO,CAAC,QAAQ,CAAC;IAgBd,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;IAMxB,gBAAgB,CACpB,aAAa,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,aAAa,GACzC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IASpB,WAAW,CAAC,CAAC,SAAS,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAY5D,cAAc,CAAC,CAAC,SAAS,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;CAKtE;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B,aAAa,EAAE,aAAa,CAAC;IAC7B,aAAa,EAAE,QAAQ,GAAG,MAAM,CAAC;IACjC,8BAA8B,EAAE,OAAO,CAAC;IACxC,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,WAAW,CAAC;IACtB,IAAI,EAAE,OAAO,EAAE,CAAC;IAChB,IAAI,CAAC,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;CAC5B;AAID;;GAEG;AACH,qBAAa,QAAQ;;IAenB,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAEf,OAAO,EAAE,eAAe;IAwDpC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAMvB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAqG7B"}