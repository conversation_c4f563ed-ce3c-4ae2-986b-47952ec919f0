{"version": 3, "file": "Connection.js", "sourceRoot": "", "sources": ["../../../../src/common/Connection.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG;AACH,iDAAyC;AACzC,yCAAiC;AACjC,MAAM,iBAAiB,GAAG,IAAA,gBAAK,EAAC,2BAA2B,CAAC,CAAC;AAC7D,MAAM,oBAAoB,GAAG,IAAA,gBAAK,EAAC,2BAA2B,CAAC,CAAC;AAKhE,uDAA+C;AAC/C,2CAA0C;AAiB1C;;;;GAIG;AACU,QAAA,uBAAuB,GAAG;IACrC,YAAY,EAAE,MAAM,CAAC,yBAAyB,CAAC;CACvC,CAAC;AAEX;;GAEG;AACH,MAAa,UAAW,SAAQ,8BAAY;IAU1C,YAAY,GAAW,EAAE,SAA8B,EAAE,KAAK,GAAG,CAAC;QAChE,KAAK,EAAE,CAAC;;QAVV,kCAAa;QACb,wCAAgC;QAChC,oCAAe;QACf,6BAAU,CAAC,EAAC;QACZ,+BAAqC,IAAI,GAAG,EAAE,EAAC;QAC/C,6BAAU,KAAK,EAAC;QAChB,gCAA8C,IAAI,GAAG,EAAE,EAAC;QACxD,uCAAoB,IAAI,GAAG,EAAU,EAAC;QAIpC,uBAAA,IAAI,mBAAQ,GAAG,MAAA,CAAC;QAChB,uBAAA,IAAI,qBAAU,KAAK,MAAA,CAAC;QAEpB,uBAAA,IAAI,yBAAc,SAAS,MAAA,CAAC;QAC5B,uBAAA,IAAI,6BAAW,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,uBAAA,IAAI,6BAAW,CAAC,OAAO,GAAG,uBAAA,IAAI,kDAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAmB;QACpC,OAAO,OAAO,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,uBAAA,IAAI,0BAAQ,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,uBAAA,IAAI,4BAAU,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,SAAiB;QACvB,OAAO,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED,GAAG;QACD,OAAO,uBAAA,IAAI,uBAAK,CAAC;IACnB,CAAC;IAED,IAAI,CACF,MAAS,EACT,GAAG,SAAoD;QAEvD,2EAA2E;QAC3E,0CAA0C;QAC1C,sFAAsF;QACtF,yEAAyE;QACzE,kBAAkB;QAClB,iFAAiF;QACjF,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC3D,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAC,MAAM,EAAE,MAAM,EAAC,CAAC,CAAC;QAC3C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,uBAAA,IAAI,6BAAW,CAAC,GAAG,CAAC,EAAE,EAAE;gBACtB,OAAO;gBACP,MAAM;gBACN,KAAK,EAAE,IAAI,yBAAa,EAAE;gBAC1B,MAAM;aACP,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,OAAgC;;QACvC,MAAM,EAAE,GAAG,iDAAA,CAAE,0DAAY,EAAd,IAAc,CAAA,MAAA,CAAC;QAC1B,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,EAAC,EAAE,EAAC,CAAC,CAAC,CAAC;QAC5E,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;QACtC,uBAAA,IAAI,6BAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,OAAe;QACvC,IAAI,uBAAA,IAAI,yBAAO,EAAE;YACf,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACpB,OAAO,UAAU,CAAC,CAAC,EAAE,uBAAA,IAAI,yBAAO,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;SACJ;QACD,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,MAAM,CAAC,MAAM,KAAK,yBAAyB,EAAE;YAC/C,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YAC1C,MAAM,OAAO,GAAG,IAAI,UAAU,CAC5B,IAAI,EACJ,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAC7B,SAAS,CACV,CAAC;YACF,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YACtC,MAAM,aAAa,GAAG,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3D,IAAI,aAAa,EAAE;gBACjB,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;aAChD;SACF;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,2BAA2B,EAAE;YACxD,MAAM,OAAO,GAAG,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC5D,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,uBAAA,IAAI,4BAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC/C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBACtC,MAAM,aAAa,GAAG,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC3D,IAAI,aAAa,EAAE;oBACjB,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;iBAChD;aACF;SACF;QACD,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,MAAM,OAAO,GAAG,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACrD,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aAC5B;SACF;aAAM,IAAI,MAAM,CAAC,EAAE,EAAE;YACpB,MAAM,QAAQ,GAAG,uBAAA,IAAI,6BAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChD,sEAAsE;YACtE,IAAI,QAAQ,EAAE;gBACZ,uBAAA,IAAI,6BAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAClC,IAAI,MAAM,CAAC,KAAK,EAAE;oBAChB,QAAQ,CAAC,MAAM,CACb,mBAAmB,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAC7D,CAAC;iBACH;qBAAM;oBACL,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;iBACjC;aACF;SACF;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACzC;IACH,CAAC;IAyBD,OAAO;QACL,uBAAA,IAAI,kDAAS,MAAb,IAAI,CAAW,CAAC;QAChB,uBAAA,IAAI,6BAAW,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAgB;QAC7B,OAAO,CAAC,uBAAA,IAAI,oCAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,UAAsC,EACtC,oBAAoB,GAAG,IAAI;QAE3B,IAAI,CAAC,oBAAoB,EAAE;YACzB,uBAAA,IAAI,oCAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;SACjD;QACD,MAAM,EAAC,SAAS,EAAC,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC3D,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QACH,uBAAA,IAAI,oCAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CACjB,UAAsC;QAEtC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;CACF;AAhND,gCAgNC;;IAjEG,IAAI,uBAAA,IAAI,0BAAQ,EAAE;QAChB,OAAO;KACR;IACD,uBAAA,IAAI,sBAAW,IAAI,MAAA,CAAC;IACpB,uBAAA,IAAI,6BAAW,CAAC,SAAS,GAAG,SAAS,CAAC;IACtC,uBAAA,IAAI,6BAAW,CAAC,OAAO,GAAG,SAAS,CAAC;IACpC,KAAK,MAAM,QAAQ,IAAI,uBAAA,IAAI,6BAAW,CAAC,MAAM,EAAE,EAAE;QAC/C,QAAQ,CAAC,MAAM,CACb,YAAY,CACV,QAAQ,CAAC,KAAK,EACd,mBAAmB,QAAQ,CAAC,MAAM,mBAAmB,CACtD,CACF,CAAC;KACH;IACD,uBAAA,IAAI,6BAAW,CAAC,KAAK,EAAE,CAAC;IACxB,KAAK,MAAM,OAAO,IAAI,uBAAA,IAAI,4BAAU,CAAC,MAAM,EAAE,EAAE;QAC7C,OAAO,CAAC,SAAS,EAAE,CAAC;KACrB;IACD,uBAAA,IAAI,4BAAU,CAAC,KAAK,EAAE,CAAC;IACvB,IAAI,CAAC,IAAI,CAAC,+BAAuB,CAAC,YAAY,CAAC,CAAC;AAClD,CAAC;AA0DH;;;;GAIG;AACU,QAAA,uBAAuB,GAAG;IACrC,YAAY,EAAE,MAAM,CAAC,yBAAyB,CAAC;CACvC,CAAC;AAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAa,UAAW,SAAQ,8BAAY;IAM1C;;OAEG;IACH,YAAY,UAAsB,EAAE,UAAkB,EAAE,SAAiB;QACvE,KAAK,EAAE,CAAC;QATV,wCAAmB;QACnB,yCAAoB;QACpB,gCAA8C,IAAI,GAAG,EAAE,EAAC;QACxD,yCAAyB;QAOvB,uBAAA,IAAI,0BAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,0BAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,yBAAc,SAAS,MAAA,CAAC;IAC9B,CAAC;IAED,UAAU;QACR,OAAO,uBAAA,IAAI,8BAAY,CAAC;IAC1B,CAAC;IAED,IAAI,CACF,MAAS,EACT,GAAG,SAAoD;QAEvD,IAAI,CAAC,uBAAA,IAAI,8BAAY,EAAE;YACrB,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CACP,mBAAmB,MAAM,sCACvB,uBAAA,IAAI,8BACN,mBAAmB,CACpB,CACF,CAAC;SACH;QAED,gEAAgE;QAChE,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE3D,MAAM,EAAE,GAAG,uBAAA,IAAI,8BAAY,CAAC,QAAQ,CAAC;YACnC,SAAS,EAAE,uBAAA,IAAI,6BAAW;YAC1B,MAAM;YACN,MAAM;SACP,CAAC,CAAC;QAEH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,uBAAA,IAAI,6BAAW,CAAC,GAAG,CAAC,EAAE,EAAE;gBACtB,OAAO;gBACP,MAAM;gBACN,KAAK,EAAE,IAAI,yBAAa,EAAE;gBAC1B,MAAM;aACP,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAAiC;QAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,uBAAA,IAAI,6BAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACxE,IAAI,MAAM,CAAC,EAAE,IAAI,QAAQ,EAAE;YACzB,uBAAA,IAAI,6BAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAClC,IAAI,MAAM,CAAC,KAAK,EAAE;gBAChB,QAAQ,CAAC,MAAM,CACb,mBAAmB,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAC7D,CAAC;aACH;iBAAM;gBACL,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aACjC;SACF;aAAM;YACL,IAAA,kBAAM,EAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACzC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,uBAAA,IAAI,8BAAY,EAAE;YACrB,MAAM,IAAI,KAAK,CACb,6CACE,uBAAA,IAAI,8BACN,mBAAmB,CACpB,CAAC;SACH;QACD,MAAM,uBAAA,IAAI,8BAAY,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACrD,SAAS,EAAE,uBAAA,IAAI,6BAAW;SAC3B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS;QACP,KAAK,MAAM,QAAQ,IAAI,uBAAA,IAAI,6BAAW,CAAC,MAAM,EAAE,EAAE;YAC/C,QAAQ,CAAC,MAAM,CACb,YAAY,CACV,QAAQ,CAAC,KAAK,EACd,mBAAmB,QAAQ,CAAC,MAAM,mBAAmB,CACtD,CACF,CAAC;SACH;QACD,uBAAA,IAAI,6BAAW,CAAC,KAAK,EAAE,CAAC;QACxB,uBAAA,IAAI,0BAAe,SAAS,MAAA,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,+BAAuB,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,EAAE;QACA,OAAO,uBAAA,IAAI,6BAAW,CAAC;IACzB,CAAC;CACF;AAjHD,gCAiHC;;AAED,SAAS,mBAAmB,CAC1B,KAAoB,EACpB,MAAc,EACd,MAA2D;IAE3D,IAAI,OAAO,GAAG,mBAAmB,MAAM,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACpE,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;QAC1B,OAAO,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;KACpC;IACD,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,YAAY,CACnB,KAAoB,EACpB,OAAe,EACf,eAAwB;IAExB,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,KAAK,CAAC,eAAe,GAAG,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,KAAK,CAAC,eAAe,CAAC;IACjE,OAAO,KAAK,CAAC;AACf,CAAC"}