{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/pagination/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Pagination from './src/pagination'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElPagination: SFCWithInstall<typeof Pagination> =\n  withInstall(Pagination)\nexport default ElPagination\n\nexport * from './src/pagination'\nexport * from './src/constants'\n"], "names": ["withInstall", "Pagination"], "mappings": ";;;;;;;;AAEY,MAAC,YAAY,GAAGA,mBAAW,CAACC,qBAAU;;;;;;;;"}