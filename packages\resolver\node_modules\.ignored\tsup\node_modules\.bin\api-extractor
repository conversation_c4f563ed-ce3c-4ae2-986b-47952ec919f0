#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules/@microsoft/api-extractor/bin/node_modules:/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules/@microsoft/api-extractor/node_modules:/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules/@microsoft/node_modules:/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules:/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules/@microsoft/api-extractor/bin/node_modules:/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules/@microsoft/api-extractor/node_modules:/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules/@microsoft/node_modules:/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules:/mnt/c/work/vue/neue-plus-copy/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules/@microsoft/api-extractor/bin/api-extractor" "$@"
else
  exec node  "$basedir/../../../../../@microsoft+api-extractor@7.52.7_@types+node@22.15.17/node_modules/@microsoft/api-extractor/bin/api-extractor" "$@"
fi
