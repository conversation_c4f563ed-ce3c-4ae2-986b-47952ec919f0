{"name": "consola", "version": "2.15.3", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> for Node.js and Browser", "license": "MIT", "repository": "nuxt/consola", "main": "dist/consola.js", "browser": "dist/consola.browser.js", "typings": "types/consola.d.ts", "scripts": {"build": "rm -rf dist && yarn build:node && yarn build:browser", "build:node": "NODE_ENV=node bili src/node.js --file-name consola.js --format cjs --bundle-node-modules --minify --no-map", "build:browser": "NODE_ENV=browser bili src/browser.js --file-name consola.browser.js --format umd --module-name consola --bundle-node-modules --minify --no-map", "demo": "node demo", "browser": "serve", "test": "yarn lint && yarn test:types && yarn build && jest test", "lint": "eslint .", "test:types": "tsc -p test/types", "prepublish": "yarn build", "release": "standard-version && yarn build && git push --follow-tags && npm publish"}, "files": ["src", "dist", "types"], "keywords": ["console", "logger", "reporter", "elegant", "cli", "universal", "unified", "winston"], "contributors": [{"name": "Pooya Parsa <<EMAIL>>"}, {"name": "<PERSON> (@clarkdo)"}, {"name": "@pimlie <<EMAIL>>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> <@Atinux>"}], "devDependencies": {"@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "babel-eslint": "^10.1.0", "babel-jest": "^26.0.1", "benchmark": "^2.1.4", "bili": "^4.10.0", "chalk": "^4.0.0", "dayjs": "^1.8.26", "eslint": "^7.1.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.9.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "esm": "^3.2.25", "figures": "^3.2.0", "jest": "^26.0.1", "lodash": "^4.17.15", "sentencer": "^0.2.1", "serve": "^11.3.0", "standard-version": "^8.0.0", "std-env": "^2.2.1", "string-width": "^4.2.0", "ts-node": "^8.10.1", "typescript": "^3.8.3", "winston": "^3.2.1"}}