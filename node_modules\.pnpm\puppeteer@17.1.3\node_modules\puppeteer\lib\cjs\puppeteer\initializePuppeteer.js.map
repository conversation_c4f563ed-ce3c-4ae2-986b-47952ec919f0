{"version": 3, "file": "initializePuppeteer.js", "sourceRoot": "", "sources": ["../../../src/initializePuppeteer.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,iDAA2C;AAC3C,sDAAkD;AAClD,iDAAmD;AACnD,0EAAkE;AAElE;;GAEG;AACI,MAAM,mBAAmB,GAAG,CAAC,WAAmB,EAAiB,EAAE;IACxE,MAAM,eAAe,GAAG,WAAW,KAAK,gBAAgB,CAAC;IACzD,IAAI,iBAAiB,GAAG,kCAAmB,CAAC,QAAQ,CAAC;IACrD,+CAA+C;IAC/C,MAAM,WAAW,GAAG,CAAC,eAAe;QAClC,CAAC,CAAE,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAa;QACpE,CAAC,CAAC,SAAS,CAAC;IAEd,IAAI,CAAC,eAAe,IAAI,WAAW,KAAK,SAAS,EAAE;QACjD,iBAAiB,GAAG,kCAAmB,CAAC,OAAO,CAAC;KACjD;IAED,OAAO,IAAI,4BAAa,CAAC;QACvB,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAA,4CAAmB,EAAC,0BAAW,CAAC;QAC3E,iBAAiB;QACjB,eAAe;QACf,WAAW;KACZ,CAAC,CAAC;AACL,CAAC,CAAC;AApBW,QAAA,mBAAmB,uBAoB9B"}