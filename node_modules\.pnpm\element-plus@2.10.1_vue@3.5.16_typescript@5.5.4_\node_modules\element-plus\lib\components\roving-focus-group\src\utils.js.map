{"version": 3, "file": "utils.js", "sources": ["../../../../../../packages/components/roving-focus-group/src/utils.ts"], "sourcesContent": ["import { EVENT_CODE } from '@element-plus/constants'\nimport type { HTMLAttributes } from 'vue'\n\ntype Orientation = HTMLAttributes['aria-orientation']\ntype Direction = 'ltr' | 'rtl'\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next'\n\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev',\n  ArrowUp: 'prev',\n  ArrowRight: 'next',\n  ArrowDown: 'next',\n  PageUp: 'first',\n  Home: 'first',\n  PageDown: 'last',\n  End: 'last',\n}\n\nconst getDirectionAwareKey = (key: string, dir?: Direction) => {\n  if (dir !== 'rtl') return key\n\n  switch (key) {\n    case EVENT_CODE.right:\n      return EVENT_CODE.left\n    case EVENT_CODE.left:\n      return EVENT_CODE.right\n    default:\n      return key\n  }\n}\n\nexport const getFocusIntent = (\n  event: KeyboardEvent,\n  orientation?: Orientation,\n  dir?: Direction\n) => {\n  const key = getDirectionAwareKey(event.code, dir)\n  if (\n    orientation === 'vertical' &&\n    [EVENT_CODE.left, EVENT_CODE.right].includes(key)\n  )\n    return undefined\n  if (\n    orientation === 'horizontal' &&\n    [EVENT_CODE.up, EVENT_CODE.down].includes(key)\n  )\n    return undefined\n  return MAP_KEY_TO_FOCUS_INTENT[key]\n}\n\nexport const reorderArray = <T>(array: T[], atIdx: number) => {\n  return array.map((_, idx) => array[(idx + atIdx) % array.length])\n}\n\nexport const focusFirst = (elements: HTMLElement[]) => {\n  const { activeElement: prevActive } = document\n\n  for (const element of elements) {\n    if (element === prevActive) return\n    element.focus()\n    if (prevActive !== document.activeElement) return\n  }\n}\n"], "names": ["EVENT_CODE"], "mappings": ";;;;;;AACA,MAAM,uBAAuB,GAAG;AAChC,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,OAAO,EAAE,MAAM;AACjB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,QAAQ,EAAE,MAAM;AAClB,EAAE,GAAG,EAAE,MAAM;AACb,CAAC,CAAC;AACF,MAAM,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK;AAC3C,EAAE,IAAI,GAAG,KAAK,KAAK;AACnB,IAAI,OAAO,GAAG,CAAC;AACf,EAAE,QAAQ,GAAG;AACb,IAAI,KAAKA,eAAU,CAAC,KAAK;AACzB,MAAM,OAAOA,eAAU,CAAC,IAAI,CAAC;AAC7B,IAAI,KAAKA,eAAU,CAAC,IAAI;AACxB,MAAM,OAAOA,eAAU,CAAC,KAAK,CAAC;AAC9B,IAAI;AACJ,MAAM,OAAO,GAAG,CAAC;AACjB,GAAG;AACH,CAAC,CAAC;AACU,MAAC,cAAc,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,GAAG,KAAK;AAC3D,EAAE,MAAM,GAAG,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACpD,EAAE,IAAI,WAAW,KAAK,UAAU,IAAI,CAACA,eAAU,CAAC,IAAI,EAAEA,eAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;AACrF,IAAI,OAAO,KAAK,CAAC,CAAC;AAClB,EAAE,IAAI,WAAW,KAAK,YAAY,IAAI,CAACA,eAAU,CAAC,EAAE,EAAEA,eAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;AACpF,IAAI,OAAO,KAAK,CAAC,CAAC;AAClB,EAAE,OAAO,uBAAuB,CAAC,GAAG,CAAC,CAAC;AACtC,EAAE;AACU,MAAC,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AAC9C,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AACpE,EAAE;AACU,MAAC,UAAU,GAAG,CAAC,QAAQ,KAAK;AACxC,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC;AACjD,EAAE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAClC,IAAI,IAAI,OAAO,KAAK,UAAU;AAC9B,MAAM,OAAO;AACb,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;AACpB,IAAI,IAAI,UAAU,KAAK,QAAQ,CAAC,aAAa;AAC7C,MAAM,OAAO;AACb,GAAG;AACH;;;;;;"}