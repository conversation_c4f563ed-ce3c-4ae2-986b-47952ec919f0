{"name": "unbzip2-stream", "version": "1.4.3", "description": "streaming unbzip2 implementation in pure javascript for node and browsers", "keywords": ["bzip", "bzip2", "bz2", "stream", "streaming", "decompress", "through"], "scripts": {"prepare": "mkdir -p dist && browserify -s unbzip2Stream index.js | uglifyjs > dist/unbzip2-stream.min.js", "browser-test": "browserify -t brfs test/simple.js | tape-run", "prepare-long-test": "head -c 104857600 < /dev/urandom | tee test/fixtures/vmlinux.bin | bzip2 > test/fixtures/vmlinux.bin.bz2", "long-test": "tape test/extra/long.js", "download-test": "beefy test/browser/long.js --open -- -t brfs", "test": "tape test/*.js"}, "author": "<PERSON> <<EMAIL>>", "repository": {"url": "https://github.com/regular/unbzip2-stream.git", "type": "git"}, "bugs": {"url": "https://github.com/regular/unbzip2-stream/issues"}, "files": ["index.js", "lib", "dist/unbzip2-stream.min.js"], "main": "index.js", "devDependencies": {"beefy": "^2.1.8", "brfs": "^1.2.0", "browserify": "^16.2.3", "concat-stream": "^1.4.7", "stream-equal": "^1.1.1", "tape": "^4.9.2", "tape-run": "^4.0.0", "uglify-js": "^3.0.10"}, "dependencies": {"buffer": "^5.2.1", "through": "^2.3.8"}, "license": "MIT"}