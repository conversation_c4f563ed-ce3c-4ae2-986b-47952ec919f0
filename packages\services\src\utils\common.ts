/**
 * 通用工具函数
 */

import {
  cloneDeep,
  get,
  debounce as lodashDebounce,
  isEmpty as lodashIsEmpty,
  throttle as lodashThrottle,
} from 'lodash-unified'
import { ApiResponse, ServiceError } from '../types'

/**
 * 判断是否为 OData 配置
 */
export function isODataConfig(config: any): boolean {
  return config.protocol === 'odata'
}

/**
 * 判断是否为 REST 配置
 */
export function isRestConfig(config: any): boolean {
  return !config.protocol || config.protocol === 'rest'
}

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  total?: number
): ApiResponse<T> {
  return {
    data,
    success: true,
    total,
  }
}

/**
 * 创建错误响应
 */
export function createErrorResponse(error: string): ApiResponse {
  return {
    data: null,
    success: false,
    error,
  }
}

/**
 * 格式化 URL
 */
export function formatUrl(baseUrl: string, path: string): string {
  const base = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl
  const pathStr = path.startsWith('/') ? path : `/${path}`
  return `${base}${pathStr}`
}

/**
 * 合并请求头
 */
export function mergeHeaders(
  ...headers: (Record<string, string> | undefined)[]
): Record<string, string> {
  return Object.assign({}, ...headers.filter(Boolean))
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: any

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error

      if (attempt === maxAttempts) {
        break
      }

      console.warn(
        `[retry] 第 ${attempt} 次尝试失败，${delayMs}ms 后重试:`,
        error.message
      )
      await delay(delayMs)
    }
  }

  throw new ServiceError(
    `重试 ${maxAttempts} 次后仍然失败: ${lastError.message}`,
    lastError.status,
    'RETRY_FAILED',
    lastError
  )
}

/**
 * 从对象中按路径提取数据
 * 使用 lodash-unified 的 get 方法，支持更复杂的路径格式
 */
export function extractDataByPath(
  data: any,
  path: string,
  defaultValue?: any
): any {
  if (!data || !path) {
    if (defaultValue !== undefined) return defaultValue
    throw new ServiceError(`无法在响应中找到路径: ${path}`)
  }

  const result = get(data, path, defaultValue)

  // 如果没有提供默认值且结果为 undefined，抛出错误
  if (result === undefined && defaultValue === undefined) {
    throw new ServiceError(`无法在响应中找到路径: ${path}`)
  }

  return result
}

/**
 * 深度克隆对象
 * 使用 lodash-unified 的 cloneDeep 方法
 */
export function deepClone<T>(obj: T): T {
  return cloneDeep(obj)
}

/**
 * 防抖函数
 * 使用 lodash-unified 的 debounce 方法
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
) {
  return lodashDebounce(func, wait)
}

/**
 * 节流函数
 * 使用 lodash-unified 的 throttle 方法
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
) {
  return lodashThrottle(func, limit)
}

/**
 * 生成唯一 ID
 */
export function generateId(): string {
  return Math.random().toString(36).slice(2, 11)
}

/**
 * 检查是否为空值
 * 使用 lodash-unified 的 isEmpty 方法，但保留字符串 trim 的逻辑
 */
export function isEmpty(value: any): boolean {
  // 对于字符串，检查 trim 后是否为空
  if (typeof value === 'string') {
    return value.trim().length === 0
  }

  // 其他类型使用 lodash-unified 的 isEmpty
  return lodashIsEmpty(value)
}

/**
 * 安全的 JSON 解析
 */
export function safeJsonParse<T = any>(str: string, defaultValue: T): T {
  try {
    return JSON.parse(str)
  } catch {
    return defaultValue
  }
}

/**
 * 安全的 JSON 字符串化
 */
export function safeJsonStringify(
  obj: any,
  defaultValue: string = '{}'
): string {
  try {
    return JSON.stringify(obj)
  } catch {
    return defaultValue
  }
}

/**
 * 类型守卫：检查是否为字符串
 */
export function isString(value: any): value is string {
  return typeof value === 'string'
}

/**
 * 类型守卫：检查是否为数字
 */
export function isNumber(value: any): value is number {
  return typeof value === 'number' && !Number.isNaN(value)
}

/**
 * 类型守卫：检查是否为布尔值
 */
export function isBoolean(value: any): value is boolean {
  return typeof value === 'boolean'
}

/**
 * 类型守卫：检查是否为对象
 */
export function isObject(value: any): value is Record<string, any> {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

/**
 * 类型守卫：检查是否为数组
 */
export function isArray(value: any): value is any[] {
  return Array.isArray(value)
}

/**
 * 类型守卫：检查是否为函数
 */
export function isFunction(value: any): value is (...args: any[]) => any {
  return typeof value === 'function'
}
