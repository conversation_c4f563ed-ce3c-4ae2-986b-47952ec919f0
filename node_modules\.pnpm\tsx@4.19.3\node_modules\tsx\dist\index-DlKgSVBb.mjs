var Pt=Object.defineProperty;var u=(n,e)=>Pt(n,"name",{value:e,configurable:!0});import{pathToFileURL as Tt}from"node:url";import{version as Se,transformSync as Zt,transform as $t}from"esbuild";import Vt from"node:crypto";import v from"node:fs";import Z from"node:path";import Wt from"node:os";import{t as zt}from"./temporary-directory-CwHp0_NW.mjs";const Je=u(n=>Vt.createHash("sha1").update(n).digest("hex"),"sha1"),Re=44,eA=59,Ue="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",xe=new Uint8Array(64),ve=new Uint8Array(128);for(let n=0;n<Ue.length;n++){const e=Ue.charCodeAt(n);xe[n]=e,ve[e]=n}const de=typeof TextDecoder<"u"?new TextDecoder:typeof Buffer<"u"?{decode(n){return Buffer.from(n.buffer,n.byteOffset,n.byteLength).toString()}}:{decode(n){let e="";for(let A=0;A<n.length;A++)e+=String.fromCharCode(n[A]);return e}};function tA(n){const e=new Int32Array(5),A=[];let i=0;do{const s=AA(n,i),a=[];let c=!0,C=0;e[0]=0;for(let Q=i;Q<s;Q++){let g;Q=$(n,Q,e,0);const f=e[0];f<C&&(c=!1),C=f,Fe(n,Q,s)?(Q=$(n,Q,e,1),Q=$(n,Q,e,2),Q=$(n,Q,e,3),Fe(n,Q,s)?(Q=$(n,Q,e,4),g=[f,e[1],e[2],e[3],e[4]]):g=[f,e[1],e[2],e[3]]):g=[f],a.push(g)}c||rA(a),A.push(a),i=s+1}while(i<=n.length);return A}u(tA,"decode");function AA(n,e){const A=n.indexOf(";",e);return A===-1?n.length:A}u(AA,"indexOf");function $(n,e,A,i){let s=0,a=0,c=0;do{const Q=n.charCodeAt(e++);c=ve[Q],s|=(c&31)<<a,a+=5}while(c&32);const C=s&1;return s>>>=1,C&&(s=-2147483648|-s),A[i]+=s,e}u($,"decodeInteger");function Fe(n,e,A){return e>=A?!1:n.charCodeAt(e)!==Re}u(Fe,"hasMoreVlq");function rA(n){n.sort(iA)}u(rA,"sort");function iA(n,e){return n[0]-e[0]}u(iA,"sortComparator$1");function Me(n){const e=new Int32Array(5),A=1024*16,i=A-36,s=new Uint8Array(A),a=s.subarray(0,i);let c=0,C="";for(let Q=0;Q<n.length;Q++){const g=n[Q];if(Q>0&&(c===A&&(C+=de.decode(s),c=0),s[c++]=eA),g.length!==0){e[0]=0;for(let f=0;f<g.length;f++){const r=g[f];c>i&&(C+=de.decode(a),s.copyWithin(0,i,c),c-=i),f>0&&(s[c++]=Re),c=V(s,c,e,r,0),r.length!==1&&(c=V(s,c,e,r,1),c=V(s,c,e,r,2),c=V(s,c,e,r,3),r.length!==4&&(c=V(s,c,e,r,4)))}}}return C+de.decode(s.subarray(0,c))}u(Me,"encode");function V(n,e,A,i,s){const a=i[s];let c=a-A[s];A[s]=a,c=c<0?-c<<1|1:c<<1;do{let C=c&31;c>>>=5,c>0&&(C|=32),n[e++]=xe[C]}while(c>0);return e}u(V,"encodeInteger");class ae{static{u(this,"BitSet")}constructor(e){this.bits=e instanceof ae?e.bits.slice():[]}add(e){this.bits[e>>5]|=1<<(e&31)}has(e){return!!(this.bits[e>>5]&1<<(e&31))}}class te{static{u(this,"Chunk")}constructor(e,A,i){this.start=e,this.end=A,this.original=i,this.intro="",this.outro="",this.content=i,this.storeName=!1,this.edited=!1,this.previous=null,this.next=null}appendLeft(e){this.outro+=e}appendRight(e){this.intro=this.intro+e}clone(){const e=new te(this.start,this.end,this.original);return e.intro=this.intro,e.outro=this.outro,e.content=this.content,e.storeName=this.storeName,e.edited=this.edited,e}contains(e){return this.start<e&&e<this.end}eachNext(e){let A=this;for(;A;)e(A),A=A.next}eachPrevious(e){let A=this;for(;A;)e(A),A=A.previous}edit(e,A,i){return this.content=e,i||(this.intro="",this.outro=""),this.storeName=A,this.edited=!0,this}prependLeft(e){this.outro=e+this.outro}prependRight(e){this.intro=e+this.intro}reset(){this.intro="",this.outro="",this.edited&&(this.content=this.original,this.storeName=!1,this.edited=!1)}split(e){const A=e-this.start,i=this.original.slice(0,A),s=this.original.slice(A);this.original=i;const a=new te(e,this.end,s);return a.outro=this.outro,this.outro="",this.end=e,this.edited?(a.edit("",!1),this.content=""):this.content=i,a.next=this.next,a.next&&(a.next.previous=a),a.previous=this,this.next=a,a}toString(){return this.intro+this.content+this.outro}trimEnd(e){if(this.outro=this.outro.replace(e,""),this.outro.length)return!0;const A=this.content.replace(e,"");if(A.length)return A!==this.content&&(this.split(this.start+A.length).edit("",void 0,!0),this.edited&&this.edit(A,this.storeName,!0)),!0;if(this.edit("",void 0,!0),this.intro=this.intro.replace(e,""),this.intro.length)return!0}trimStart(e){if(this.intro=this.intro.replace(e,""),this.intro.length)return!0;const A=this.content.replace(e,"");if(A.length){if(A!==this.content){const i=this.split(this.end-A.length);this.edited&&i.edit(A,this.storeName,!0),this.edit("",void 0,!0)}return!0}else if(this.edit("",void 0,!0),this.outro=this.outro.replace(e,""),this.outro.length)return!0}}function nA(){return typeof globalThis<"u"&&typeof globalThis.btoa=="function"?n=>globalThis.btoa(unescape(encodeURIComponent(n))):typeof Buffer=="function"?n=>Buffer.from(n,"utf-8").toString("base64"):()=>{throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")}}u(nA,"getBtoa");const sA=nA();let oA=class{static{u(this,"SourceMap")}constructor(e){this.version=3,this.file=e.file,this.sources=e.sources,this.sourcesContent=e.sourcesContent,this.names=e.names,this.mappings=Me(e.mappings),typeof e.x_google_ignoreList<"u"&&(this.x_google_ignoreList=e.x_google_ignoreList)}toString(){return JSON.stringify(this)}toUrl(){return"data:application/json;charset=utf-8;base64,"+sA(this.toString())}};function aA(n){const e=n.split(`
`),A=e.filter(a=>/^\t+/.test(a)),i=e.filter(a=>/^ {2,}/.test(a));if(A.length===0&&i.length===0)return null;if(A.length>=i.length)return"	";const s=i.reduce((a,c)=>{const C=/^ +/.exec(c)[0].length;return Math.min(C,a)},1/0);return new Array(s+1).join(" ")}u(aA,"guessIndent");function cA(n,e){const A=n.split(/[/\\]/),i=e.split(/[/\\]/);for(A.pop();A[0]===i[0];)A.shift(),i.shift();if(A.length){let s=A.length;for(;s--;)A[s]=".."}return A.concat(i).join("/")}u(cA,"getRelativePath");const hA=Object.prototype.toString;function lA(n){return hA.call(n)==="[object Object]"}u(lA,"isObject");function Ge(n){const e=n.split(`
`),A=[];for(let i=0,s=0;i<e.length;i++)A.push(s),s+=e[i].length+1;return u(function(s){let a=0,c=A.length;for(;a<c;){const g=a+c>>1;s<A[g]?c=g:a=g+1}const C=a-1,Q=s-A[C];return{line:C,column:Q}},"locate")}u(Ge,"getLocator");const uA=/\w/;class fA{static{u(this,"Mappings")}constructor(e){this.hires=e,this.generatedCodeLine=0,this.generatedCodeColumn=0,this.raw=[],this.rawSegments=this.raw[this.generatedCodeLine]=[],this.pending=null}addEdit(e,A,i,s){if(A.length){const a=A.length-1;let c=A.indexOf(`
`,0),C=-1;for(;c>=0&&a>c;){const g=[this.generatedCodeColumn,e,i.line,i.column];s>=0&&g.push(s),this.rawSegments.push(g),this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,C=c,c=A.indexOf(`
`,c+1)}const Q=[this.generatedCodeColumn,e,i.line,i.column];s>=0&&Q.push(s),this.rawSegments.push(Q),this.advance(A.slice(C+1))}else this.pending&&(this.rawSegments.push(this.pending),this.advance(A));this.pending=null}addUneditedChunk(e,A,i,s,a){let c=A.start,C=!0,Q=!1;for(;c<A.end;){if(this.hires||C||a.has(c)){const g=[this.generatedCodeColumn,e,s.line,s.column];this.hires==="boundary"?uA.test(i[c])?Q||(this.rawSegments.push(g),Q=!0):(this.rawSegments.push(g),Q=!1):this.rawSegments.push(g)}i[c]===`
`?(s.line+=1,s.column=0,this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,C=!0):(s.column+=1,this.generatedCodeColumn+=1,C=!1),c+=1}this.pending=null}advance(e){if(!e)return;const A=e.split(`
`);if(A.length>1){for(let i=0;i<A.length-1;i++)this.generatedCodeLine++,this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}this.generatedCodeColumn+=A[A.length-1].length}}const W=`
`,q={insertLeft:!1,insertRight:!1,storeName:!1};class Ke{static{u(this,"MagicString")}constructor(e,A={}){const i=new te(0,e.length,e);Object.defineProperties(this,{original:{writable:!0,value:e},outro:{writable:!0,value:""},intro:{writable:!0,value:""},firstChunk:{writable:!0,value:i},lastChunk:{writable:!0,value:i},lastSearchedChunk:{writable:!0,value:i},byStart:{writable:!0,value:{}},byEnd:{writable:!0,value:{}},filename:{writable:!0,value:A.filename},indentExclusionRanges:{writable:!0,value:A.indentExclusionRanges},sourcemapLocations:{writable:!0,value:new ae},storedNames:{writable:!0,value:{}},indentStr:{writable:!0,value:void 0},ignoreList:{writable:!0,value:A.ignoreList}}),this.byStart[0]=i,this.byEnd[e.length]=i}addSourcemapLocation(e){this.sourcemapLocations.add(e)}append(e){if(typeof e!="string")throw new TypeError("outro content must be a string");return this.outro+=e,this}appendLeft(e,A){if(typeof A!="string")throw new TypeError("inserted content must be a string");this._split(e);const i=this.byEnd[e];return i?i.appendLeft(A):this.intro+=A,this}appendRight(e,A){if(typeof A!="string")throw new TypeError("inserted content must be a string");this._split(e);const i=this.byStart[e];return i?i.appendRight(A):this.outro+=A,this}clone(){const e=new Ke(this.original,{filename:this.filename});let A=this.firstChunk,i=e.firstChunk=e.lastSearchedChunk=A.clone();for(;A;){e.byStart[i.start]=i,e.byEnd[i.end]=i;const s=A.next,a=s&&s.clone();a&&(i.next=a,a.previous=i,i=a),A=s}return e.lastChunk=i,this.indentExclusionRanges&&(e.indentExclusionRanges=this.indentExclusionRanges.slice()),e.sourcemapLocations=new ae(this.sourcemapLocations),e.intro=this.intro,e.outro=this.outro,e}generateDecodedMap(e){e=e||{};const A=0,i=Object.keys(this.storedNames),s=new fA(e.hires),a=Ge(this.original);return this.intro&&s.advance(this.intro),this.firstChunk.eachNext(c=>{const C=a(c.start);c.intro.length&&s.advance(c.intro),c.edited?s.addEdit(A,c.content,C,c.storeName?i.indexOf(c.original):-1):s.addUneditedChunk(A,c,this.original,C,this.sourcemapLocations),c.outro.length&&s.advance(c.outro)}),{file:e.file?e.file.split(/[/\\]/).pop():void 0,sources:[e.source?cA(e.file||"",e.source):e.file||""],sourcesContent:e.includeContent?[this.original]:void 0,names:i,mappings:s.raw,x_google_ignoreList:this.ignoreList?[A]:void 0}}generateMap(e){return new oA(this.generateDecodedMap(e))}_ensureindentStr(){this.indentStr===void 0&&(this.indentStr=aA(this.original))}_getRawIndentString(){return this._ensureindentStr(),this.indentStr}getIndentString(){return this._ensureindentStr(),this.indentStr===null?"	":this.indentStr}indent(e,A){const i=/^[^\r\n]/gm;if(lA(e)&&(A=e,e=void 0),e===void 0&&(this._ensureindentStr(),e=this.indentStr||"	"),e==="")return this;A=A||{};const s={};A.exclude&&(typeof A.exclude[0]=="number"?[A.exclude]:A.exclude).forEach(f=>{for(let r=f[0];r<f[1];r+=1)s[r]=!0});let a=A.indentStart!==!1;const c=u(g=>a?`${e}${g}`:(a=!0,g),"replacer");this.intro=this.intro.replace(i,c);let C=0,Q=this.firstChunk;for(;Q;){const g=Q.end;if(Q.edited)s[C]||(Q.content=Q.content.replace(i,c),Q.content.length&&(a=Q.content[Q.content.length-1]===`
`));else for(C=Q.start;C<g;){if(!s[C]){const f=this.original[C];f===`
`?a=!0:f!=="\r"&&a&&(a=!1,C===Q.start||(this._splitChunk(Q,C),Q=Q.next),Q.prependRight(e))}C+=1}C=Q.end,Q=Q.next}return this.outro=this.outro.replace(i,c),this}insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")}insertLeft(e,A){return q.insertLeft||(console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead"),q.insertLeft=!0),this.appendLeft(e,A)}insertRight(e,A){return q.insertRight||(console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead"),q.insertRight=!0),this.prependRight(e,A)}move(e,A,i){if(i>=e&&i<=A)throw new Error("Cannot move a selection inside itself");this._split(e),this._split(A),this._split(i);const s=this.byStart[e],a=this.byEnd[A],c=s.previous,C=a.next,Q=this.byStart[i];if(!Q&&a===this.lastChunk)return this;const g=Q?Q.previous:this.lastChunk;return c&&(c.next=C),C&&(C.previous=c),g&&(g.next=s),Q&&(Q.previous=a),s.previous||(this.firstChunk=a.next),a.next||(this.lastChunk=s.previous,this.lastChunk.next=null),s.previous=g,a.next=Q||null,g||(this.firstChunk=s),Q||(this.lastChunk=a),this}overwrite(e,A,i,s){return s=s||{},this.update(e,A,i,{...s,overwrite:!s.contentOnly})}update(e,A,i,s){if(typeof i!="string")throw new TypeError("replacement content must be a string");for(;e<0;)e+=this.original.length;for(;A<0;)A+=this.original.length;if(A>this.original.length)throw new Error("end is out of bounds");if(e===A)throw new Error("Cannot overwrite a zero-length range \u2013 use appendLeft or prependRight instead");this._split(e),this._split(A),s===!0&&(q.storeName||(console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string"),q.storeName=!0),s={storeName:!0});const a=s!==void 0?s.storeName:!1,c=s!==void 0?s.overwrite:!1;if(a){const g=this.original.slice(e,A);Object.defineProperty(this.storedNames,g,{writable:!0,value:!0,enumerable:!0})}const C=this.byStart[e],Q=this.byEnd[A];if(C){let g=C;for(;g!==Q;){if(g.next!==this.byStart[g.end])throw new Error("Cannot overwrite across a split point");g=g.next,g.edit("",!1)}C.edit(i,a,!c)}else{const g=new te(e,A,"").edit(i,a);Q.next=g,g.previous=Q}return this}prepend(e){if(typeof e!="string")throw new TypeError("outro content must be a string");return this.intro=e+this.intro,this}prependLeft(e,A){if(typeof A!="string")throw new TypeError("inserted content must be a string");this._split(e);const i=this.byEnd[e];return i?i.prependLeft(A):this.intro=A+this.intro,this}prependRight(e,A){if(typeof A!="string")throw new TypeError("inserted content must be a string");this._split(e);const i=this.byStart[e];return i?i.prependRight(A):this.outro=A+this.outro,this}remove(e,A){for(;e<0;)e+=this.original.length;for(;A<0;)A+=this.original.length;if(e===A)return this;if(e<0||A>this.original.length)throw new Error("Character is out of bounds");if(e>A)throw new Error("end must be greater than start");this._split(e),this._split(A);let i=this.byStart[e];for(;i;)i.intro="",i.outro="",i.edit(""),i=A>i.end?this.byStart[i.end]:null;return this}reset(e,A){for(;e<0;)e+=this.original.length;for(;A<0;)A+=this.original.length;if(e===A)return this;if(e<0||A>this.original.length)throw new Error("Character is out of bounds");if(e>A)throw new Error("end must be greater than start");this._split(e),this._split(A);let i=this.byStart[e];for(;i;)i.reset(),i=A>i.end?this.byStart[i.end]:null;return this}lastChar(){if(this.outro.length)return this.outro[this.outro.length-1];let e=this.lastChunk;do{if(e.outro.length)return e.outro[e.outro.length-1];if(e.content.length)return e.content[e.content.length-1];if(e.intro.length)return e.intro[e.intro.length-1]}while(e=e.previous);return this.intro.length?this.intro[this.intro.length-1]:""}lastLine(){let e=this.outro.lastIndexOf(W);if(e!==-1)return this.outro.substr(e+1);let A=this.outro,i=this.lastChunk;do{if(i.outro.length>0){if(e=i.outro.lastIndexOf(W),e!==-1)return i.outro.substr(e+1)+A;A=i.outro+A}if(i.content.length>0){if(e=i.content.lastIndexOf(W),e!==-1)return i.content.substr(e+1)+A;A=i.content+A}if(i.intro.length>0){if(e=i.intro.lastIndexOf(W),e!==-1)return i.intro.substr(e+1)+A;A=i.intro+A}}while(i=i.previous);return e=this.intro.lastIndexOf(W),e!==-1?this.intro.substr(e+1)+A:this.intro+A}slice(e=0,A=this.original.length){for(;e<0;)e+=this.original.length;for(;A<0;)A+=this.original.length;let i="",s=this.firstChunk;for(;s&&(s.start>e||s.end<=e);){if(s.start<A&&s.end>=A)return i;s=s.next}if(s&&s.edited&&s.start!==e)throw new Error(`Cannot use replaced character ${e} as slice start anchor.`);const a=s;for(;s;){s.intro&&(a!==s||s.start===e)&&(i+=s.intro);const c=s.start<A&&s.end>=A;if(c&&s.edited&&s.end!==A)throw new Error(`Cannot use replaced character ${A} as slice end anchor.`);const C=a===s?e-s.start:0,Q=c?s.content.length+A-s.end:s.content.length;if(i+=s.content.slice(C,Q),s.outro&&(!c||s.end===A)&&(i+=s.outro),c)break;s=s.next}return i}snip(e,A){const i=this.clone();return i.remove(0,e),i.remove(A,i.original.length),i}_split(e){if(this.byStart[e]||this.byEnd[e])return;let A=this.lastSearchedChunk;const i=e>A.end;for(;A;){if(A.contains(e))return this._splitChunk(A,e);A=i?this.byStart[A.end]:this.byEnd[A.start]}}_splitChunk(e,A){if(e.edited&&e.content.length){const s=Ge(this.original)(A);throw new Error(`Cannot split a chunk that has already been edited (${s.line}:${s.column} \u2013 "${e.original}")`)}const i=e.split(A);return this.byEnd[A]=e,this.byStart[A]=i,this.byEnd[i.end]=i,e===this.lastChunk&&(this.lastChunk=i),this.lastSearchedChunk=e,!0}toString(){let e=this.intro,A=this.firstChunk;for(;A;)e+=A.toString(),A=A.next;return e+this.outro}isEmpty(){let e=this.firstChunk;do if(e.intro.length&&e.intro.trim()||e.content.length&&e.content.trim()||e.outro.length&&e.outro.trim())return!1;while(e=e.next);return!0}length(){let e=this.firstChunk,A=0;do A+=e.intro.length+e.content.length+e.outro.length;while(e=e.next);return A}trimLines(){return this.trim("[\\r\\n]")}trim(e){return this.trimStart(e).trimEnd(e)}trimEndAborted(e){const A=new RegExp((e||"\\s")+"+$");if(this.outro=this.outro.replace(A,""),this.outro.length)return!0;let i=this.lastChunk;do{const s=i.end,a=i.trimEnd(A);if(i.end!==s&&(this.lastChunk===i&&(this.lastChunk=i.next),this.byEnd[i.end]=i,this.byStart[i.next.start]=i.next,this.byEnd[i.next.end]=i.next),a)return!0;i=i.previous}while(i);return!1}trimEnd(e){return this.trimEndAborted(e),this}trimStartAborted(e){const A=new RegExp("^"+(e||"\\s")+"+");if(this.intro=this.intro.replace(A,""),this.intro.length)return!0;let i=this.firstChunk;do{const s=i.end,a=i.trimStart(A);if(i.end!==s&&(i===this.lastChunk&&(this.lastChunk=i.next),this.byEnd[i.end]=i,this.byStart[i.next.start]=i.next,this.byEnd[i.next.end]=i.next),a)return!0;i=i.next}while(i);return!1}trimStart(e){return this.trimStartAborted(e),this}hasChanged(){return this.original!==this.toString()}_replaceRegexp(e,A){function i(a,c){return typeof A=="string"?A.replace(/\$(\$|&|\d+)/g,(C,Q)=>Q==="$"?"$":Q==="&"?a[0]:+Q<a.length?a[+Q]:`$${Q}`):A(...a,a.index,c,a.groups)}u(i,"getReplacement");function s(a,c){let C;const Q=[];for(;C=a.exec(c);)Q.push(C);return Q}if(u(s,"matchAll"),e.global)s(e,this.original).forEach(c=>{if(c.index!=null){const C=i(c,this.original);C!==c[0]&&this.overwrite(c.index,c.index+c[0].length,C)}});else{const a=this.original.match(e);if(a&&a.index!=null){const c=i(a,this.original);c!==a[0]&&this.overwrite(a.index,a.index+a[0].length,c)}}return this}_replaceString(e,A){const{original:i}=this,s=i.indexOf(e);return s!==-1&&this.overwrite(s,s+e.length,A),this}replace(e,A){return typeof e=="string"?this._replaceString(e,A):this._replaceRegexp(e,A)}_replaceAllString(e,A){const{original:i}=this,s=e.length;for(let a=i.indexOf(e);a!==-1;a=i.indexOf(e,a+s))i.slice(a,a+s)!==A&&this.overwrite(a,a+s,A);return this}replaceAll(e,A){if(typeof e=="string")return this._replaceAllString(e,A);if(!e.global)throw new TypeError("MagicString.prototype.replaceAll called with a non-global RegExp argument");return this._replaceRegexp(e,A)}}var Ye;(function(n){n[n.Static=1]="Static",n[n.Dynamic=2]="Dynamic",n[n.ImportMeta=3]="ImportMeta",n[n.StaticSourcePhase=4]="StaticSourcePhase",n[n.DynamicSourcePhase=5]="DynamicSourcePhase"})(Ye||(Ye={}));const QA=new Uint8Array(new Uint16Array([1]).buffer)[0]===1;function He(n,e="@"){if(!w)return _e.then(()=>He(n));const A=n.length+1,i=(w.__heap_base.value||w.__heap_base)+4*A-w.memory.buffer.byteLength;i>0&&w.memory.grow(Math.ceil(i/65536));const s=w.sa(A-1);if((QA?CA:gA)(n,new Uint16Array(w.memory.buffer,s,A)),!w.parse())throw Object.assign(new Error(`Parse error ${e}:${n.slice(0,w.e()).split(`
`).length}:${w.e()-n.lastIndexOf(`
`,w.e()-1)}`),{idx:w.e()});const a=[],c=[];for(;w.ri();){const Q=w.is(),g=w.ie(),f=w.it(),r=w.ai(),m=w.id(),K=w.ss(),p=w.se();let S;w.ip()&&(S=C(n.slice(m===-1?Q-1:Q,m===-1?g+1:g))),a.push({n:S,t:f,s:Q,e:g,ss:K,se:p,d:m,a:r})}for(;w.re();){const Q=w.es(),g=w.ee(),f=w.els(),r=w.ele(),m=n.slice(Q,g),K=m[0],p=f<0?void 0:n.slice(f,r),S=p?p[0]:"";c.push({s:Q,e:g,ls:f,le:r,n:K==='"'||K==="'"?C(m):m,ln:S==='"'||S==="'"?C(p):p})}function C(Q){try{return(0,eval)(Q)}catch{}}return u(C,"k"),[a,c,!!w.f(),!!w.ms()]}u(He,"parse$1");function gA(n,e){const A=n.length;let i=0;for(;i<A;){const s=n.charCodeAt(i);e[i++]=(255&s)<<8|s>>>8}}u(gA,"Q");function CA(n,e){const A=n.length;let i=0;for(;i<A;)e[i]=n.charCodeAt(i++)}u(CA,"B");let w;const _e=WebAssembly.compile((we="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",typeof Buffer<"u"?Buffer.from(we,"base64"):Uint8Array.from(atob(we),n=>n.charCodeAt(0)))).then(WebAssembly.instantiate).then(({exports:n})=>{w=n});var we;let L,ie,ke,z=2<<19;const Oe=new Uint8Array(new Uint16Array([1]).buffer)[0]===1?function(n,e){const A=n.length;let i=0;for(;i<A;)e[i]=n.charCodeAt(i++)}:function(n,e){const A=n.length;let i=0;for(;i<A;){const s=n.charCodeAt(i);e[i++]=(255&s)<<8|s>>>8}},EA="xportmportlassforetaourceromsyncunctionssertvoyiedelecontininstantybreareturdebuggeawaithrwhileifcatcfinallels";let y,qe,k;function BA(n,e="@"){y=n,qe=e;const A=2*y.length+(2<<18);if(A>z||!L){for(;A>z;)z*=2;ie=new ArrayBuffer(z),Oe(EA,new Uint16Array(ie,16,110)),L=function(c,C,Q){var g=new c.Int8Array(Q),f=new c.Int16Array(Q),r=new c.Int32Array(Q),m=new c.Uint8Array(Q),K=new c.Uint16Array(Q),p=1040;function S(){var t=0,o=0,l=0,h=0,E=0,B=0,I=0;I=p,p=p+10240|0,g[804]=1,g[803]=0,f[399]=0,f[400]=0,r[69]=r[2],g[805]=0,r[68]=0,g[802]=0,r[70]=I+2048,r[71]=I,g[806]=0,t=(r[3]|0)+-2|0,r[72]=t,o=t+(r[66]<<1)|0,r[73]=o;e:for(;;){if(l=t+2|0,r[72]=l,t>>>0>=o>>>0){h=18;break}t:do switch(f[l>>1]|0){case 9:case 10:case 11:case 12:case 13:case 32:break;case 101:{if(!(f[400]|0)&&P(l)|0&&!(J(t+4|0,16,10)|0)&&(x(),(g[804]|0)==0)){h=9;break e}else h=17;break}case 105:{P(l)|0&&!(J(t+4|0,26,10)|0)&&H(),h=17;break}case 59:{h=17;break}case 47:switch(f[t+4>>1]|0){case 47:{fe();break t}case 42:{le(1);break t}default:{h=16;break e}}default:{h=16;break e}}while(!1);(h|0)==17&&(h=0,r[69]=r[72]),t=r[72]|0,o=r[73]|0}(h|0)==9?(t=r[72]|0,r[69]=t,h=19):(h|0)==16?(g[804]=0,r[72]=t,h=19):(h|0)==18&&(g[802]|0?t=0:(t=l,h=19));do if((h|0)==19){e:for(;;){if(o=t+2|0,r[72]=o,t>>>0>=(r[73]|0)>>>0){h=92;break}t:do switch(f[o>>1]|0){case 9:case 10:case 11:case 12:case 13:case 32:break;case 101:{!(f[400]|0)&&P(o)|0&&!(J(t+4|0,16,10)|0)&&x(),h=91;break}case 105:{P(o)|0&&!(J(t+4|0,26,10)|0)&&H(),h=91;break}case 99:{P(o)|0&&!(J(t+4|0,36,8)|0)&&O(f[t+12>>1]|0)|0&&(g[806]=1),h=91;break}case 40:{l=r[70]|0,t=f[400]|0,h=t&65535,r[l+(h<<3)>>2]=1,o=r[69]|0,f[400]=t+1<<16>>16,r[l+(h<<3)+4>>2]=o,h=91;break}case 41:{if(o=f[400]|0,!(o<<16>>16)){h=36;break e}l=o+-1<<16>>16,f[400]=l,h=f[399]|0,o=h&65535,h<<16>>16&&(r[(r[70]|0)+((l&65535)<<3)>>2]|0)==5&&(o=r[(r[71]|0)+(o+-1<<2)>>2]|0,l=o+4|0,r[l>>2]|0||(r[l>>2]=(r[69]|0)+2),r[o+12>>2]=t+4,f[399]=h+-1<<16>>16),h=91;break}case 123:{h=r[69]|0,l=r[63]|0,t=h;do if((f[h>>1]|0)==41&(l|0)!=0&&(r[l+4>>2]|0)==(h|0))if(o=r[64]|0,r[63]=o,o){r[o+32>>2]=0;break}else{r[59]=0;break}while(!1);l=r[70]|0,o=f[400]|0,h=o&65535,r[l+(h<<3)>>2]=g[806]|0?6:2,f[400]=o+1<<16>>16,r[l+(h<<3)+4>>2]=t,g[806]=0,h=91;break}case 125:{if(t=f[400]|0,!(t<<16>>16)){h=49;break e}l=r[70]|0,h=t+-1<<16>>16,f[400]=h,(r[l+((h&65535)<<3)>>2]|0)==4&&De(),h=91;break}case 39:{R(39),h=91;break}case 34:{R(34),h=91;break}case 47:switch(f[t+4>>1]|0){case 47:{fe();break t}case 42:{le(1);break t}default:{t=r[69]|0,o=f[t>>1]|0;A:do if(!(bt(o)|0))o<<16>>16==41?(l=f[400]|0,Lt(r[(r[70]|0)+((l&65535)<<3)+4>>2]|0)|0||(h=65)):h=64;else switch(o<<16>>16){case 46:if(((f[t+-2>>1]|0)+-48&65535)<10){h=64;break A}else break A;case 43:if((f[t+-2>>1]|0)==43){h=64;break A}else break A;case 45:if((f[t+-2>>1]|0)==45){h=64;break A}else break A;default:break A}while(!1);(h|0)==64&&(l=f[400]|0,h=65);A:do if((h|0)==65){if(h=0,l<<16>>16&&(E=r[70]|0,B=(l&65535)+-1|0,o<<16>>16==102?(r[E+(B<<3)>>2]|0)==1:0)){if((f[t+-2>>1]|0)==111&&N(r[E+(B<<3)+4>>2]|0,44,3)|0)break}else h=69;if((h|0)==69&&o<<16>>16==125&&(h=r[70]|0,l=l&65535,pt(r[h+(l<<3)+4>>2]|0)|0||(r[h+(l<<3)>>2]|0)==6))break;if(!(kt(t)|0)){switch(o<<16>>16){case 0:break A;case 47:{if(g[805]|0)break A;break}default:}if(h=r[65]|0,h|0&&t>>>0>=(r[h>>2]|0)>>>0&&t>>>0<=(r[h+4>>2]|0)>>>0){he(),g[805]=0,h=91;break t}l=r[3]|0;do{if(t>>>0<=l>>>0)break;t=t+-2|0,r[69]=t,o=f[t>>1]|0}while(!(ue(o)|0));if(re(o)|0){do{if(t>>>0<=l>>>0)break;t=t+-2|0,r[69]=t}while(re(f[t>>1]|0)|0);if(Kt(t)|0){he(),g[805]=0,h=91;break t}}g[805]=1,h=91;break t}}while(!1);he(),g[805]=0,h=91;break t}}case 96:{l=r[70]|0,o=f[400]|0,h=o&65535,r[l+(h<<3)+4>>2]=r[69],f[400]=o+1<<16>>16,r[l+(h<<3)>>2]=3,De(),h=91;break}default:h=91}while(!1);(h|0)==91&&(h=0,r[69]=r[72]),t=r[72]|0}if((h|0)==36){U(),t=0;break}else if((h|0)==49){U(),t=0;break}else if((h|0)==92){t=g[802]|0?0:(f[399]|f[400])<<16>>16==0;break}}while(!1);return p=I,t|0}u(S,"b");function x(){var t=0,o=0,l=0,h=0,E=0,B=0,I=0,G=0,ge=0,Ce=0,Ee=0,Be=0,b=0,D=0;G=r[72]|0,ge=r[65]|0,D=G+12|0,r[72]=D,l=d(1)|0,t=r[72]|0,(t|0)==(D|0)&&!(Ae(l)|0)||(b=3);e:do if((b|0)==3){t:do switch(l<<16>>16){case 123:{for(r[72]=t+2,t=d(1)|0,o=r[72]|0;;){if(T(t)|0?(R(t),t=(r[72]|0)+2|0,r[72]=t):(F(t)|0,t=r[72]|0),d(1)|0,t=Le(o,t)|0,t<<16>>16==44&&(r[72]=(r[72]|0)+2,t=d(1)|0),t<<16>>16==125){b=15;break}if(D=o,o=r[72]|0,(o|0)==(D|0)){b=12;break}if(o>>>0>(r[73]|0)>>>0){b=14;break}}if((b|0)==12){U();break e}else if((b|0)==14){U();break e}else if((b|0)==15){g[803]=1,r[72]=(r[72]|0)+2;break t}break}case 42:{r[72]=t+2,d(1)|0,D=r[72]|0,Le(D,D)|0;break}default:{switch(g[804]=0,l<<16>>16){case 100:{switch(G=t+14|0,r[72]=G,(d(1)|0)<<16>>16){case 97:{o=r[72]|0,!(J(o+2|0,72,8)|0)&&(E=o+10|0,re(f[E>>1]|0)|0)&&(r[72]=E,d(0)|0,b=22);break}case 102:{b=22;break}case 99:{o=r[72]|0,!(J(o+2|0,36,8)|0)&&(h=o+10|0,D=f[h>>1]|0,O(D)|0|D<<16>>16==123)&&(r[72]=h,B=d(1)|0,B<<16>>16!=123)&&(Be=B,b=31);break}default:}A:do if((b|0)==22&&(I=r[72]|0,(J(I+2|0,80,14)|0)==0)){if(l=I+16|0,o=f[l>>1]|0,!(O(o)|0))switch(o<<16>>16){case 40:case 42:break;default:break A}r[72]=l,o=d(1)|0,o<<16>>16==42&&(r[72]=(r[72]|0)+2,o=d(1)|0),o<<16>>16!=40&&(Be=o,b=31)}while(!1);if((b|0)==31&&(Ce=r[72]|0,F(Be)|0,Ee=r[72]|0,Ee>>>0>Ce>>>0)){_(t,G,Ce,Ee),r[72]=(r[72]|0)+-2;break e}_(t,G,0,0),r[72]=t+12;break e}case 97:{r[72]=t+10,d(0)|0,t=r[72]|0,b=35;break}case 102:{b=35;break}case 99:{if(!(J(t+2|0,36,8)|0)&&(o=t+10|0,ue(f[o>>1]|0)|0)){r[72]=o,D=d(1)|0,b=r[72]|0,F(D)|0,D=r[72]|0,_(b,D,b,D),r[72]=(r[72]|0)+-2;break e}t=t+4|0,r[72]=t;break}case 108:case 118:break;default:break e}if((b|0)==35){r[72]=t+16,t=d(1)|0,t<<16>>16==42&&(r[72]=(r[72]|0)+2,t=d(1)|0),b=r[72]|0,F(t)|0,D=r[72]|0,_(b,D,b,D),r[72]=(r[72]|0)+-2;break e}r[72]=t+6,g[804]=0,l=d(1)|0,t=r[72]|0,l=(F(l)|0|32)<<16>>16==123,h=r[72]|0,l&&(r[72]=h+2,D=d(1)|0,t=r[72]|0,F(D)|0);A:for(;o=r[72]|0,(o|0)!=(t|0);){if(_(t,o,t,o),o=d(1)|0,l)switch(o<<16>>16){case 93:case 125:break e;default:}if(t=r[72]|0,o<<16>>16!=44){b=51;break}switch(r[72]=t+2,o=d(1)|0,t=r[72]|0,o<<16>>16){case 91:case 123:{b=51;break A}default:}F(o)|0}if((b|0)==51&&(r[72]=t+-2),!l)break e;r[72]=h+-2;break e}}while(!1);if(D=(d(1)|0)<<16>>16==102,t=r[72]|0,D&&!(J(t+2|0,66,6)|0))for(r[72]=t+8,j(G,d(1)|0,0),t=ge|0?ge+16|0:240;;){if(t=r[t>>2]|0,!t)break e;r[t+12>>2]=0,r[t+8>>2]=0,t=t+16|0}r[72]=t+-2}while(!1)}u(x,"k");function H(){var t=0,o=0,l=0,h=0,E=0,B=0,I=0;E=r[72]|0,l=E+12|0,r[72]=l,h=d(1)|0,o=r[72]|0;e:do if(h<<16>>16!=46)h<<16>>16==115&o>>>0>l>>>0?!(J(o+2|0,56,10)|0)&&(t=o+12|0,O(f[t>>1]|0)|0)?B=14:(o=6,l=0,B=46):(t=h,l=0,B=15);else switch(r[72]=o+2,(d(1)|0)<<16>>16){case 109:{if(t=r[72]|0,J(t+2|0,50,6)|0||(o=r[69]|0,!(Qe(o)|0)&&(f[o>>1]|0)==46))break e;ce(E,E,t+8|0,2);break e}case 115:{if(t=r[72]|0,J(t+2|0,56,10)|0||(o=r[69]|0,!(Qe(o)|0)&&(f[o>>1]|0)==46))break e;t=t+12|0,B=14;break e}default:break e}while(!1);(B|0)==14&&(r[72]=t,t=d(1)|0,l=1,B=15);e:do if((B|0)==15)switch(t<<16>>16){case 40:{if(o=r[70]|0,I=f[400]|0,h=I&65535,r[o+(h<<3)>>2]=5,t=r[72]|0,f[400]=I+1<<16>>16,r[o+(h<<3)+4>>2]=t,(f[r[69]>>1]|0)==46)break e;switch(r[72]=t+2,o=d(1)|0,ce(E,r[72]|0,0,t),l?(t=r[63]|0,r[t+28>>2]=5):t=r[63]|0,E=r[71]|0,I=f[399]|0,f[399]=I+1<<16>>16,r[E+((I&65535)<<2)>>2]=t,o<<16>>16){case 39:{R(39);break}case 34:{R(34);break}default:{r[72]=(r[72]|0)+-2;break e}}switch(t=(r[72]|0)+2|0,r[72]=t,(d(1)|0)<<16>>16){case 44:{r[72]=(r[72]|0)+2,d(1)|0,E=r[63]|0,r[E+4>>2]=t,I=r[72]|0,r[E+16>>2]=I,g[E+24>>0]=1,r[72]=I+-2;break e}case 41:{f[400]=(f[400]|0)+-1<<16>>16,I=r[63]|0,r[I+4>>2]=t,r[I+12>>2]=(r[72]|0)+2,g[I+24>>0]=1,f[399]=(f[399]|0)+-1<<16>>16;break e}default:{r[72]=(r[72]|0)+-2;break e}}}case 123:{if(l){o=12,l=1,B=46;break e}if(t=r[72]|0,f[400]|0){r[72]=t+-2;break e}for(;!(t>>>0>=(r[73]|0)>>>0);){if(t=d(1)|0,T(t)|0)R(t);else if(t<<16>>16==125){B=36;break}t=(r[72]|0)+2|0,r[72]=t}if((B|0)==36&&(r[72]=(r[72]|0)+2),I=(d(1)|0)<<16>>16==102,t=r[72]|0,I&&J(t+2|0,66,6)|0){U();break e}if(r[72]=t+8,t=d(1)|0,T(t)|0){j(E,t,0);break e}else{U();break e}}default:{if(l){o=12,l=1,B=46;break e}switch(t<<16>>16){case 42:case 39:case 34:{l=0,B=48;break e}default:{o=6,l=0,B=46;break e}}}}while(!1);(B|0)==46&&(t=r[72]|0,(t|0)==(E+(o<<1)|0)?r[72]=t+-2:B=48);do if((B|0)==48){if(f[400]|0){r[72]=(r[72]|0)+-2;break}for(t=r[73]|0,o=r[72]|0;;){if(o>>>0>=t>>>0){B=55;break}if(h=f[o>>1]|0,T(h)|0){B=53;break}I=o+2|0,r[72]=I,o=I}if((B|0)==53){j(E,h,l);break}else if((B|0)==55){U();break}}while(!1)}u(H,"l");function j(t,o,l){t=t|0,o=o|0,l=l|0;var h=0,E=0;switch(h=(r[72]|0)+2|0,o<<16>>16){case 39:{R(39),E=5;break}case 34:{R(34),E=5;break}default:U()}do if((E|0)==5){if(ce(t,h,r[72]|0,1),l&&(r[(r[63]|0)+28>>2]=4),r[72]=(r[72]|0)+2,o=d(0)|0,l=o<<16>>16==97,l?(h=r[72]|0,J(h+2|0,94,10)|0&&(E=13)):(h=r[72]|0,o<<16>>16==119&&(f[h+2>>1]|0)==105&&(f[h+4>>1]|0)==116&&(f[h+6>>1]|0)==104||(E=13)),(E|0)==13){r[72]=h+-2;break}if(r[72]=h+((l?6:4)<<1),(d(1)|0)<<16>>16!=123){r[72]=h;break}l=r[72]|0,o=l;e:for(;;){switch(r[72]=o+2,o=d(1)|0,o<<16>>16){case 39:{R(39),r[72]=(r[72]|0)+2,o=d(1)|0;break}case 34:{R(34),r[72]=(r[72]|0)+2,o=d(1)|0;break}default:o=F(o)|0}if(o<<16>>16!=58){E=22;break}switch(r[72]=(r[72]|0)+2,(d(1)|0)<<16>>16){case 39:{R(39);break}case 34:{R(34);break}default:{E=26;break e}}switch(r[72]=(r[72]|0)+2,(d(1)|0)<<16>>16){case 125:{E=31;break e}case 44:break;default:{E=30;break e}}if(r[72]=(r[72]|0)+2,(d(1)|0)<<16>>16==125){E=31;break}o=r[72]|0}if((E|0)==22){r[72]=h;break}else if((E|0)==26){r[72]=h;break}else if((E|0)==30){r[72]=h;break}else if((E|0)==31){E=r[63]|0,r[E+16>>2]=l,r[E+12>>2]=(r[72]|0)+2;break}}while(!1)}u(j,"u");function kt(t){t=t|0;e:do switch(f[t>>1]|0){case 100:switch(f[t+-2>>1]|0){case 105:{t=N(t+-4|0,104,2)|0;break e}case 108:{t=N(t+-4|0,108,3)|0;break e}default:{t=0;break e}}case 101:switch(f[t+-2>>1]|0){case 115:switch(f[t+-4>>1]|0){case 108:{t=X(t+-6|0,101)|0;break e}case 97:{t=X(t+-6|0,99)|0;break e}default:{t=0;break e}}case 116:{t=N(t+-4|0,114,4)|0;break e}case 117:{t=N(t+-4|0,122,6)|0;break e}default:{t=0;break e}}case 102:{if((f[t+-2>>1]|0)==111&&(f[t+-4>>1]|0)==101)switch(f[t+-6>>1]|0){case 99:{t=N(t+-8|0,134,6)|0;break e}case 112:{t=N(t+-8|0,146,2)|0;break e}default:{t=0;break e}}else t=0;break}case 107:{t=N(t+-2|0,150,4)|0;break}case 110:{t=t+-2|0,X(t,105)|0?t=1:t=N(t,158,5)|0;break}case 111:{t=X(t+-2|0,100)|0;break}case 114:{t=N(t+-2|0,168,7)|0;break}case 116:{t=N(t+-2|0,182,4)|0;break}case 119:switch(f[t+-2>>1]|0){case 101:{t=X(t+-4|0,110)|0;break e}case 111:{t=N(t+-4|0,190,3)|0;break e}default:{t=0;break e}}default:t=0}while(!1);return t|0}u(kt,"o");function De(){var t=0,o=0,l=0,h=0;o=r[73]|0,l=r[72]|0;e:for(;;){if(t=l+2|0,l>>>0>=o>>>0){o=10;break}switch(f[t>>1]|0){case 96:{o=7;break e}case 36:{if((f[l+4>>1]|0)==123){o=6;break e}break}case 92:{t=l+4|0;break}default:}l=t}(o|0)==6?(t=l+4|0,r[72]=t,o=r[70]|0,h=f[400]|0,l=h&65535,r[o+(l<<3)>>2]=4,f[400]=h+1<<16>>16,r[o+(l<<3)+4>>2]=t):(o|0)==7?(r[72]=t,l=r[70]|0,h=(f[400]|0)+-1<<16>>16,f[400]=h,(r[l+((h&65535)<<3)>>2]|0)!=3&&U()):(o|0)==10&&(r[72]=t,U())}u(De,"h");function d(t){t=t|0;var o=0,l=0,h=0;l=r[72]|0;e:do{o=f[l>>1]|0;t:do if(o<<16>>16!=47)if(t){if(O(o)|0)break;break e}else{if(re(o)|0)break;break e}else switch(f[l+2>>1]|0){case 47:{fe();break t}case 42:{le(t);break t}default:{o=47;break e}}while(!1);h=r[72]|0,l=h+2|0,r[72]=l}while(h>>>0<(r[73]|0)>>>0);return o|0}u(d,"w");function ce(t,o,l,h){t=t|0,o=o|0,l=l|0,h=h|0;var E=0,B=0;B=r[67]|0,r[67]=B+36,E=r[63]|0,r[(E|0?E+32|0:236)>>2]=B,r[64]=E,r[63]=B,r[B+8>>2]=t,(h|0)==2?(t=3,E=l):(E=(h|0)==1,t=E?1:2,E=E?l+2|0:0),r[B+12>>2]=E,r[B+28>>2]=t,r[B>>2]=o,r[B+4>>2]=l,r[B+16>>2]=0,r[B+20>>2]=h,o=(h|0)==1,g[B+24>>0]=o&1,r[B+32>>2]=0,o|(h|0)==2&&(g[803]=1)}u(ce,"d");function R(t){t=t|0;var o=0,l=0,h=0,E=0;for(E=r[73]|0,o=r[72]|0;;){if(h=o+2|0,o>>>0>=E>>>0){o=9;break}if(l=f[h>>1]|0,l<<16>>16==t<<16>>16){o=10;break}if(l<<16>>16==92)l=o+4|0,(f[l>>1]|0)==13?(o=o+6|0,o=(f[o>>1]|0)==10?o:l):o=l;else if(Ne(l)|0){o=9;break}else o=h}(o|0)==9?(r[72]=h,U()):(o|0)==10&&(r[72]=h)}u(R,"v");function Le(t,o){t=t|0,o=o|0;var l=0,h=0,E=0,B=0;return l=r[72]|0,h=f[l>>1]|0,B=(t|0)==(o|0),E=B?0:t,B=B?0:o,h<<16>>16==97&&(r[72]=l+4,l=d(1)|0,t=r[72]|0,T(l)|0?(R(l),o=(r[72]|0)+2|0,r[72]=o):(F(l)|0,o=r[72]|0),h=d(1)|0,l=r[72]|0),(l|0)!=(t|0)&&_(t,o,E,B),h|0}u(Le,"A");function It(){var t=0,o=0,l=0;l=r[73]|0,o=r[72]|0;e:for(;;){if(t=o+2|0,o>>>0>=l>>>0){o=6;break}switch(f[t>>1]|0){case 13:case 10:{o=6;break e}case 93:{o=7;break e}case 92:{t=o+4|0;break}default:}o=t}return(o|0)==6?(r[72]=t,U(),t=0):(o|0)==7&&(r[72]=t,t=93),t|0}u(It,"C");function he(){var t=0,o=0,l=0;e:for(;;){if(t=r[72]|0,o=t+2|0,r[72]=o,t>>>0>=(r[73]|0)>>>0){l=7;break}switch(f[o>>1]|0){case 13:case 10:{l=7;break e}case 47:break e;case 91:{It()|0;break}case 92:{r[72]=t+4;break}default:}}(l|0)==7&&U()}u(he,"g");function pt(t){switch(t=t|0,f[t>>1]|0){case 62:{t=(f[t+-2>>1]|0)==61;break}case 41:case 59:{t=1;break}case 104:{t=N(t+-2|0,210,4)|0;break}case 121:{t=N(t+-2|0,218,6)|0;break}case 101:{t=N(t+-2|0,230,3)|0;break}default:t=0}return t|0}u(pt,"p");function le(t){t=t|0;var o=0,l=0,h=0,E=0,B=0;for(E=(r[72]|0)+2|0,r[72]=E,l=r[73]|0;o=E+2|0,!(E>>>0>=l>>>0||(h=f[o>>1]|0,!t&&Ne(h)|0));){if(h<<16>>16==42&&(f[E+4>>1]|0)==47){B=8;break}E=o}(B|0)==8&&(r[72]=o,o=E+4|0),r[72]=o}u(le,"y");function J(t,o,l){t=t|0,o=o|0,l=l|0;var h=0,E=0;e:do if(!l)t=0;else{for(;h=g[t>>0]|0,E=g[o>>0]|0,h<<24>>24==E<<24>>24;)if(l=l+-1|0,l)t=t+1|0,o=o+1|0;else{t=0;break e}t=(h&255)-(E&255)|0}while(!1);return t|0}u(J,"m");function Ae(t){t=t|0;e:do switch(t<<16>>16){case 38:case 37:case 33:{t=1;break}default:if((t&-8)<<16>>16==40|(t+-58&65535)<6)t=1;else{switch(t<<16>>16){case 91:case 93:case 94:{t=1;break e}default:}t=(t+-123&65535)<4}}while(!1);return t|0}u(Ae,"I");function bt(t){t=t|0;e:do switch(t<<16>>16){case 38:case 37:case 33:break;default:if(!((t+-58&65535)<6|(t+-40&65535)<7&t<<16>>16!=41)){switch(t<<16>>16){case 91:case 94:break e;default:}return t<<16>>16!=125&(t+-123&65535)<4|0}}while(!1);return 1}u(bt,"U");function ye(t){t=t|0;var o=0;o=f[t>>1]|0;e:do if((o+-9&65535)>=5){switch(o<<16>>16){case 160:case 32:{o=1;break e}default:}if(Ae(o)|0)return o<<16>>16!=46|(Qe(t)|0)|0;o=0}else o=1;while(!1);return o|0}u(ye,"x");function mt(t){t=t|0;var o=0,l=0,h=0,E=0;return l=p,p=p+16|0,h=l,r[h>>2]=0,r[66]=t,o=r[3]|0,E=o+(t<<1)|0,t=E+2|0,f[E>>1]=0,r[h>>2]=t,r[67]=t,r[59]=0,r[63]=0,r[61]=0,r[60]=0,r[65]=0,r[62]=0,p=l,o|0}u(mt,"S");function _(t,o,l,h){t=t|0,o=o|0,l=l|0,h=h|0;var E=0,B=0;E=r[67]|0,r[67]=E+20,B=r[65]|0,r[(B|0?B+16|0:240)>>2]=E,r[65]=E,r[E>>2]=t,r[E+4>>2]=o,r[E+8>>2]=l,r[E+12>>2]=h,r[E+16>>2]=0,g[803]=1}u(_,"O");function N(t,o,l){t=t|0,o=o|0,l=l|0;var h=0,E=0;return h=t+(0-l<<1)|0,E=h+2|0,t=r[3]|0,E>>>0>=t>>>0&&!(J(E,o,l<<1)|0)?(E|0)==(t|0)?t=1:t=ye(h)|0:t=0,t|0}u(N,"$");function Kt(t){switch(t=t|0,f[t>>1]|0){case 107:{t=N(t+-2|0,150,4)|0;break}case 101:{(f[t+-2>>1]|0)==117?t=N(t+-4|0,122,6)|0:t=0;break}default:t=0}return t|0}u(Kt,"j");function X(t,o){t=t|0,o=o|0;var l=0;return l=r[3]|0,l>>>0<=t>>>0&&(f[t>>1]|0)==o<<16>>16?(l|0)==(t|0)?l=1:l=ue(f[t+-2>>1]|0)|0:l=0,l|0}u(X,"B");function ue(t){t=t|0;e:do if((t+-9&65535)<5)t=1;else{switch(t<<16>>16){case 32:case 160:{t=1;break e}default:}t=t<<16>>16!=46&(Ae(t)|0)}while(!1);return t|0}u(ue,"E");function fe(){var t=0,o=0,l=0;t=r[73]|0,l=r[72]|0;e:for(;o=l+2|0,!(l>>>0>=t>>>0);)switch(f[o>>1]|0){case 13:case 10:break e;default:l=o}r[72]=o}u(fe,"P");function F(t){for(t=t|0;!(O(t)|0||Ae(t)|0);)if(t=(r[72]|0)+2|0,r[72]=t,t=f[t>>1]|0,!(t<<16>>16)){t=0;break}return t|0}u(F,"q");function Dt(){var t=0;switch(t=r[(r[61]|0)+20>>2]|0,t|0){case 1:{t=-1;break}case 2:{t=-2;break}default:t=t-(r[3]|0)>>1}return t|0}u(Dt,"z");function Lt(t){return t=t|0,!(N(t,196,5)|0)&&!(N(t,44,3)|0)?t=N(t,206,2)|0:t=1,t|0}u(Lt,"D");function re(t){switch(t=t|0,t<<16>>16){case 160:case 32:case 12:case 11:case 9:{t=1;break}default:t=0}return t|0}u(re,"F");function Qe(t){return t=t|0,(f[t>>1]|0)==46&&(f[t+-2>>1]|0)==46?t=(f[t+-4>>1]|0)==46:t=0,t|0}u(Qe,"G");function P(t){return t=t|0,(r[3]|0)==(t|0)?t=1:t=ye(t+-2|0)|0,t|0}u(P,"H");function yt(){var t=0;return t=r[(r[62]|0)+12>>2]|0,t?t=t-(r[3]|0)>>1:t=-1,t|0}u(yt,"J");function Nt(){var t=0;return t=r[(r[61]|0)+12>>2]|0,t?t=t-(r[3]|0)>>1:t=-1,t|0}u(Nt,"K");function St(){var t=0;return t=r[(r[62]|0)+8>>2]|0,t?t=t-(r[3]|0)>>1:t=-1,t|0}u(St,"L");function Jt(){var t=0;return t=r[(r[61]|0)+16>>2]|0,t?t=t-(r[3]|0)>>1:t=-1,t|0}u(Jt,"M");function Rt(){var t=0;return t=r[(r[61]|0)+4>>2]|0,t?t=t-(r[3]|0)>>1:t=-1,t|0}u(Rt,"N");function Ut(){var t=0;return t=r[61]|0,t=r[(t|0?t+32|0:236)>>2]|0,r[61]=t,(t|0)!=0|0}u(Ut,"Q");function xt(){var t=0;return t=r[62]|0,t=r[(t|0?t+16|0:240)>>2]|0,r[62]=t,(t|0)!=0|0}u(xt,"R");function U(){g[802]=1,r[68]=(r[72]|0)-(r[3]|0)>>1,r[72]=(r[73]|0)+2}u(U,"T");function O(t){return t=t|0,(t|128)<<16>>16==160|(t+-9&65535)<5|0}u(O,"V");function T(t){return t=t|0,t<<16>>16==39|t<<16>>16==34|0}u(T,"W");function vt(){return(r[(r[61]|0)+8>>2]|0)-(r[3]|0)>>1|0}u(vt,"X");function Ft(){return(r[(r[62]|0)+4>>2]|0)-(r[3]|0)>>1|0}u(Ft,"Y");function Ne(t){return t=t|0,t<<16>>16==13|t<<16>>16==10|0}u(Ne,"Z");function Mt(){return(r[r[61]>>2]|0)-(r[3]|0)>>1|0}u(Mt,"_");function Gt(){return(r[r[62]>>2]|0)-(r[3]|0)>>1|0}u(Gt,"ee");function Yt(){return m[(r[61]|0)+24>>0]|0|0}u(Yt,"ae");function Ht(t){t=t|0,r[3]=t}u(Ht,"re");function _t(){return r[(r[61]|0)+28>>2]|0}u(_t,"ie");function Ot(){return(g[803]|0)!=0|0}u(Ot,"se");function qt(){return(g[804]|0)!=0|0}u(qt,"fe");function jt(){return r[68]|0}u(jt,"te");function Xt(t){return t=t|0,p=t+992+15&-16,992}return u(Xt,"ce"),{su:Xt,ai:Jt,e:jt,ee:Ft,ele:yt,els:St,es:Gt,f:qt,id:Dt,ie:Rt,ip:Yt,is:Mt,it:_t,ms:Ot,p:S,re:xt,ri:Ut,sa:mt,se:Nt,ses:Ht,ss:vt}}(typeof self<"u"?self:global,{},ie),ke=L.su(z-(2<<17))}const i=y.length+1;L.ses(ke),L.sa(i-1),Oe(y,new Uint16Array(ie,ke,i)),L.p()||(k=L.e(),M());const s=[],a=[];for(;L.ri();){const c=L.is(),C=L.ie(),Q=L.ai(),g=L.id(),f=L.ss(),r=L.se(),m=L.it();let K;L.ip()&&(K=Ie(g===-1?c:c+1,y.charCodeAt(g===-1?c-1:c))),s.push({t:m,n:K,s:c,e:C,ss:f,se:r,d:g,a:Q})}for(;L.re();){const c=L.es(),C=L.ee(),Q=L.els(),g=L.ele(),f=y.charCodeAt(c),r=Q>=0?y.charCodeAt(Q):-1;a.push({s:c,e:C,ls:Q,le:g,n:f===34||f===39?Ie(c+1,f):y.slice(c,C),ln:Q<0?void 0:r===34||r===39?Ie(Q+1,r):y.slice(Q,g)})}return[s,a,!!L.f(),!!L.ms()]}u(BA,"parse");function Ie(n,e){k=n;let A="",i=k;for(;;){k>=y.length&&M();const s=y.charCodeAt(k);if(s===e)break;s===92?(A+=y.slice(i,k),A+=dA(),i=k):(s===8232||s===8233||je(s)&&M(),++k)}return A+=y.slice(i,k++),A}u(Ie,"b");function dA(){let n=y.charCodeAt(++k);switch(++k,n){case 110:return`
`;case 114:return"\r";case 120:return String.fromCharCode(pe(2));case 117:return function(){const e=y.charCodeAt(k);let A;return e===123?(++k,A=pe(y.indexOf("}",k)-k),++k,A>1114111&&M()):A=pe(4),A<=65535?String.fromCharCode(A):(A-=65536,String.fromCharCode(55296+(A>>10),56320+(1023&A)))}();case 116:return"	";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:y.charCodeAt(k)===10&&++k;case 10:return"";case 56:case 57:M();default:if(n>=48&&n<=55){let e=y.substr(k-1,3).match(/^[0-7]+/)[0],A=parseInt(e,8);return A>255&&(e=e.slice(0,-1),A=parseInt(e,8)),k+=e.length-1,n=y.charCodeAt(k),e==="0"&&n!==56&&n!==57||M(),String.fromCharCode(A)}return je(n)?"":String.fromCharCode(n)}}u(dA,"k");function pe(n){const e=k;let A=0,i=0;for(let s=0;s<n;++s,++k){let a,c=y.charCodeAt(k);if(c!==95){if(c>=97)a=c-97+10;else if(c>=65)a=c-65+10;else{if(!(c>=48&&c<=57))break;a=c-48}if(a>=16)break;i=c,A=16*A+a}else i!==95&&s!==0||M(),i=c}return i!==95&&k-e===n||M(),A}u(pe,"l");function je(n){return n===13||n===10}u(je,"u");function M(){throw Object.assign(Error(`Parse error ${qe}:${y.slice(0,k).split(`
`).length}:${k-y.lastIndexOf(`
`,k-1)}`),{idx:k})}u(M,"o");let Xe=!1;_e.then(()=>{Xe=!0});const Pe=u((n,e)=>Xe?He(n,e):BA(n,e),"parseEsm"),wA=u(n=>{if(!n.includes("import")&&!n.includes("export"))return!1;try{return Pe(n)[3]}catch{return!0}},"isESM"),Te="2",kA=(n=>{const e="default";return n[e]&&typeof n[e]=="object"&&"__esModule"in n[e]?n[e]:n}).toString(),IA=`.then(${kA})`,be=u((n,e,A)=>{if(A){if(!e.includes("import("))return}else if(!e.includes("import"))return;const s=Pe(e,n)[0].filter(Q=>Q.d>-1);if(s.length===0)return;const a=new Ke(e);for(const Q of s)a.appendRight(Q.se,IA);const c=a.toString(),C=a.generateMap({source:n,includeContent:!1,hires:"boundary"});return{code:c,map:C}},"transformDynamicImport"),Ze=u(n=>{try{const e=v.readFileSync(n,"utf8");return JSON.parse(e)}catch{}},"readJsonFile"),$e=u(()=>{},"noop"),Ve=u(()=>Math.floor(Date.now()/1e8),"getTime");class pA extends Map{static{u(this,"FileCache")}cacheDirectory=zt;oldCacheDirectory=Z.join(Wt.tmpdir(),"tsx");cacheFiles;constructor(){super(),v.mkdirSync(this.cacheDirectory,{recursive:!0}),this.cacheFiles=v.readdirSync(this.cacheDirectory).map(e=>{const[A,i]=e.split("-");return{time:Number(A),key:i,fileName:e}}),setImmediate(()=>{this.expireDiskCache(),this.removeOldCacheDirectory()})}get(e){const A=super.get(e);if(A)return A;const i=this.cacheFiles.find(c=>c.key===e);if(!i)return;const s=Z.join(this.cacheDirectory,i.fileName),a=Ze(s);if(!a){v.promises.unlink(s).then(()=>{const c=this.cacheFiles.indexOf(i);this.cacheFiles.splice(c,1)},()=>{});return}return super.set(e,a),a}set(e,A){if(super.set(e,A),A){const i=Ve();v.promises.writeFile(Z.join(this.cacheDirectory,`${i}-${e}`),JSON.stringify(A)).catch($e)}return this}expireDiskCache(){const e=Ve();for(const A of this.cacheFiles)e-A.time>7&&v.promises.unlink(Z.join(this.cacheDirectory,A.fileName)).catch($e)}async removeOldCacheDirectory(){try{await v.promises.access(this.oldCacheDirectory).then(()=>!0)&&("rm"in v.promises?await v.promises.rm(this.oldCacheDirectory,{recursive:!0,force:!0}):await v.promises.rmdir(this.oldCacheDirectory,{recursive:!0}))}catch{}}}var ne=process.env.TSX_DISABLE_CACHE?new Map:new pA;const bA=/^[\w+.-]+:\/\//,mA=/^([\w+.-]+:)\/\/([^@/#?]*@)?([^:/#?]*)(:\d+)?(\/[^#?]*)?(\?[^#]*)?(#.*)?/,KA=/^file:(?:\/\/((?![a-z]:)[^/#?]*)?)?(\/?[^#?]*)(\?[^#]*)?(#.*)?/i;function DA(n){return bA.test(n)}u(DA,"isAbsoluteUrl");function LA(n){return n.startsWith("//")}u(LA,"isSchemeRelativeUrl");function We(n){return n.startsWith("/")}u(We,"isAbsolutePath");function yA(n){return n.startsWith("file:")}u(yA,"isFileUrl");function ze(n){return/^[.?#]/.test(n)}u(ze,"isRelative");function se(n){const e=mA.exec(n);return et(e[1],e[2]||"",e[3],e[4]||"",e[5]||"/",e[6]||"",e[7]||"")}u(se,"parseAbsoluteUrl");function NA(n){const e=KA.exec(n),A=e[2];return et("file:","",e[1]||"","",We(A)?A:"/"+A,e[3]||"",e[4]||"")}u(NA,"parseFileUrl");function et(n,e,A,i,s,a,c){return{scheme:n,user:e,host:A,port:i,path:s,query:a,hash:c,type:7}}u(et,"makeUrl");function tt(n){if(LA(n)){const A=se("http:"+n);return A.scheme="",A.type=6,A}if(We(n)){const A=se("http://foo.com"+n);return A.scheme="",A.host="",A.type=5,A}if(yA(n))return NA(n);if(DA(n))return se(n);const e=se("http://foo.com/"+n);return e.scheme="",e.host="",e.type=n?n.startsWith("?")?3:n.startsWith("#")?2:4:1,e}u(tt,"parseUrl");function SA(n){if(n.endsWith("/.."))return n;const e=n.lastIndexOf("/");return n.slice(0,e+1)}u(SA,"stripPathFilename");function JA(n,e){At(e,e.type),n.path==="/"?n.path=e.path:n.path=SA(e.path)+n.path}u(JA,"mergePaths");function At(n,e){const A=e<=4,i=n.path.split("/");let s=1,a=0,c=!1;for(let Q=1;Q<i.length;Q++){const g=i[Q];if(!g){c=!0;continue}if(c=!1,g!=="."){if(g===".."){a?(c=!0,a--,s--):A&&(i[s++]=g);continue}i[s++]=g,a++}}let C="";for(let Q=1;Q<s;Q++)C+="/"+i[Q];(!C||c&&!C.endsWith("/.."))&&(C+="/"),n.path=C}u(At,"normalizePath");function RA(n,e){if(!n&&!e)return"";const A=tt(n);let i=A.type;if(e&&i!==7){const a=tt(e),c=a.type;switch(i){case 1:A.hash=a.hash;case 2:A.query=a.query;case 3:case 4:JA(A,a);case 5:A.user=a.user,A.host=a.host,A.port=a.port;case 6:A.scheme=a.scheme}c>i&&(i=c)}At(A,i);const s=A.query+A.hash;switch(i){case 2:case 3:return s;case 4:{const a=A.path.slice(1);return a?ze(e||n)&&!ze(a)?"./"+a+s:a+s:s||"."}case 5:return A.path+s;default:return A.scheme+"//"+A.user+A.host+A.port+A.path+s}}u(RA,"resolve$1");function rt(n,e){return e&&!e.endsWith("/")&&(e+="/"),RA(n,e)}u(rt,"resolve");function UA(n){if(!n)return"";const e=n.lastIndexOf("/");return n.slice(0,e+1)}u(UA,"stripFilename");const Y=0;function xA(n,e){const A=it(n,0);if(A===n.length)return n;e||(n=n.slice());for(let i=A;i<n.length;i=it(n,i+1))n[i]=FA(n[i],e);return n}u(xA,"maybeSort");function it(n,e){for(let A=e;A<n.length;A++)if(!vA(n[A]))return A;return n.length}u(it,"nextUnsortedSegmentLine");function vA(n){for(let e=1;e<n.length;e++)if(n[e][Y]<n[e-1][Y])return!1;return!0}u(vA,"isSorted");function FA(n,e){return e||(n=n.slice()),n.sort(MA)}u(FA,"sortSegments");function MA(n,e){return n[Y]-e[Y]}u(MA,"sortComparator");let oe=!1;function GA(n,e,A,i){for(;A<=i;){const s=A+(i-A>>1),a=n[s][Y]-e;if(a===0)return oe=!0,s;a<0?A=s+1:i=s-1}return oe=!1,A-1}u(GA,"binarySearch");function YA(n,e,A){for(let i=A-1;i>=0&&n[i][Y]===e;A=i--);return A}u(YA,"lowerBound");function HA(){return{lastKey:-1,lastNeedle:-1,lastIndex:-1}}u(HA,"memoizedState");function _A(n,e,A,i){const{lastKey:s,lastNeedle:a,lastIndex:c}=A;let C=0,Q=n.length-1;if(i===s){if(e===a)return oe=c!==-1&&n[c][Y]===e,c;e>=a?C=c===-1?0:c:Q=c}return A.lastKey=i,A.lastNeedle=e,A.lastIndex=GA(n,e,C,Q)}u(_A,"memoizedBinarySearch");class nt{static{u(this,"TraceMap")}constructor(e,A){const i=typeof e=="string";if(!i&&e._decodedMemo)return e;const s=i?JSON.parse(e):e,{version:a,file:c,names:C,sourceRoot:Q,sources:g,sourcesContent:f}=s;this.version=a,this.file=c,this.names=C||[],this.sourceRoot=Q,this.sources=g,this.sourcesContent=f,this.ignoreList=s.ignoreList||s.x_google_ignoreList||void 0;const r=rt(Q||"",UA(A));this.resolvedSources=g.map(K=>rt(K||"",r));const{mappings:m}=s;typeof m=="string"?(this._encoded=m,this._decoded=void 0):(this._encoded=void 0,this._decoded=xA(m,i)),this._decodedMemo=HA(),this._bySources=void 0,this._bySourceMemos=void 0}}function Nr(n){return n}u(Nr,"cast$2");function st(n){var e;return(e=n)._decoded||(e._decoded=tA(n._encoded))}u(st,"decodedMappings");function OA(n,e,A){const i=st(n);if(e>=i.length)return null;const s=i[e],a=qA(s,n._decodedMemo,e,A);return a===-1?null:s[a]}u(OA,"traceSegment");function qA(n,e,A,i,s){let a=_A(n,i,e,A);return oe&&(a=YA(n,i,a)),a===-1||a===n.length?-1:a}u(qA,"traceSegmentInternal");class me{static{u(this,"SetArray")}constructor(){this._indexes={__proto__:null},this.array=[]}}function Sr(n){return n}u(Sr,"cast$1");function ot(n,e){return n._indexes[e]}u(ot,"get");function ee(n,e){const A=ot(n,e);if(A!==void 0)return A;const{array:i,_indexes:s}=n,a=i.push(e);return s[e]=a-1}u(ee,"put");function jA(n,e){const A=ot(n,e);if(A===void 0)return;const{array:i,_indexes:s}=n;for(let a=A+1;a<i.length;a++){const c=i[a];i[a-1]=c,s[c]--}s[e]=void 0,i.pop()}u(jA,"remove");const XA=0,PA=1,TA=2,ZA=3,$A=4,at=-1;class VA{static{u(this,"GenMapping")}constructor({file:e,sourceRoot:A}={}){this._names=new me,this._sources=new me,this._sourcesContent=[],this._mappings=[],this.file=e,this.sourceRoot=A,this._ignoreList=new me}}function Jr(n){return n}u(Jr,"cast");const WA=u((n,e,A,i,s,a,c,C)=>Ar(!0,n,e,A,i,s,a,c),"maybeAddSegment");function zA(n,e,A){const{_sources:i,_sourcesContent:s}=n,a=ee(i,e);s[a]=A}u(zA,"setSourceContent");function er(n,e,A=!0){const{_sources:i,_sourcesContent:s,_ignoreList:a}=n,c=ee(i,e);c===s.length&&(s[c]=null),A?ee(a,c):jA(a,c)}u(er,"setIgnore");function ct(n){const{_mappings:e,_sources:A,_sourcesContent:i,_names:s,_ignoreList:a}=n;return nr(e),{version:3,file:n.file||void 0,names:s.array,sourceRoot:n.sourceRoot||void 0,sources:A.array,sourcesContent:i,mappings:e,ignoreList:a.array}}u(ct,"toDecodedMap");function tr(n){const e=ct(n);return Object.assign(Object.assign({},e),{mappings:Me(e.mappings)})}u(tr,"toEncodedMap");function Ar(n,e,A,i,s,a,c,C,Q){const{_mappings:g,_sources:f,_sourcesContent:r,_names:m}=e,K=rr(g,A),p=ir(K,i);if(!s)return sr(K,p)?void 0:ht(K,p,[i]);const S=ee(f,s),x=C?ee(m,C):at;if(S===r.length&&(r[S]=null),!or(K,p,S,a,c,x))return ht(K,p,C?[i,S,a,c,x]:[i,S,a,c])}u(Ar,"addSegmentInternal");function rr(n,e){for(let A=n.length;A<=e;A++)n[A]=[];return n[e]}u(rr,"getLine");function ir(n,e){let A=n.length;for(let i=A-1;i>=0;A=i--){const s=n[i];if(e>=s[XA])break}return A}u(ir,"getColumnIndex");function ht(n,e,A){for(let i=n.length;i>e;i--)n[i]=n[i-1];n[e]=A}u(ht,"insert");function nr(n){const{length:e}=n;let A=e;for(let i=A-1;i>=0&&!(n[i].length>0);A=i,i--);A<e&&(n.length=A)}u(nr,"removeEmptyFinalLines");function sr(n,e){return e===0?!0:n[e-1].length===1}u(sr,"skipSourceless");function or(n,e,A,i,s,a){if(e===0)return!1;const c=n[e-1];return c.length===1?!1:A===c[PA]&&i===c[TA]&&s===c[ZA]&&a===(c.length===5?c[$A]:at)}u(or,"skipSource");const lt=ut("",-1,-1,"",null,!1),ar=[];function ut(n,e,A,i,s,a){return{source:n,line:e,column:A,name:i,content:s,ignore:a}}u(ut,"SegmentObject");function ft(n,e,A,i,s){return{map:n,sources:e,source:A,content:i,ignore:s}}u(ft,"Source");function Qt(n,e){return ft(n,e,"",null,!1)}u(Qt,"MapSource");function cr(n,e,A){return ft(null,ar,n,e,A)}u(cr,"OriginalSource");function hr(n){const e=new VA({file:n.map.file}),{sources:A,map:i}=n,s=i.names,a=st(i);for(let c=0;c<a.length;c++){const C=a[c];for(let Q=0;Q<C.length;Q++){const g=C[Q],f=g[0];let r=lt;if(g.length!==1){const j=A[g[1]];if(r=gt(j,g[2],g[3],g.length===5?s[g[4]]:""),r==null)continue}const{column:m,line:K,name:p,content:S,source:x,ignore:H}=r;WA(e,c,f,x,K,m,p),x&&S!=null&&zA(e,x,S),H&&er(e,x,!0)}}return e}u(hr,"traceMappings");function gt(n,e,A,i){if(!n.map)return ut(n.source,e,A,i,n.content,n.ignore);const s=OA(n.map,e,A);return s==null?null:s.length===1?lt:gt(n.sources[s[1]],s[2],s[3],s.length===5?n.map.names[s[4]]:i)}u(gt,"originalPositionFor");function lr(n){return Array.isArray(n)?n:[n]}u(lr,"asArray");function ur(n,e){const A=lr(n).map(a=>new nt(a,"")),i=A.pop();for(let a=0;a<A.length;a++)if(A[a].sources.length>1)throw new Error(`Transformation map ${a} must have exactly one source file.
Did you specify these with the most recent transformation maps first?`);let s=Ct(i,e,"",0);for(let a=A.length-1;a>=0;a--)s=Qt(A[a],[s]);return s}u(ur,"buildSourceMapTree");function Ct(n,e,A,i){const{resolvedSources:s,sourcesContent:a,ignoreList:c}=n,C=i+1,Q=s.map((g,f)=>{const r={importer:A,depth:C,source:g||"",content:void 0,ignore:void 0},m=e(r.source,r),{source:K,content:p,ignore:S}=r;if(m)return Ct(new nt(m,K),e,K,C);const x=p!==void 0?p:a?a[f]:null,H=S!==void 0?S:c?c.includes(f):!1;return cr(K,x,H)});return Qt(n,Q)}u(Ct,"build");class fr{static{u(this,"SourceMap")}constructor(e,A){const i=A.decodedMappings?ct(e):tr(e);this.version=i.version,this.file=i.file,this.mappings=i.mappings,this.names=i.names,this.ignoreList=i.ignoreList,this.sourceRoot=i.sourceRoot,this.sources=i.sources,A.excludeContent||(this.sourcesContent=i.sourcesContent)}toString(){return JSON.stringify(this)}}function Et(n,e,A){const i={excludeContent:!!A,decodedMappings:!1},s=ur(n,e);return new fr(hr(s),i)}u(Et,"remapping");const Qr=u((n,e,A)=>{const i=[],s={code:e};for(const a of A){const c=a(n,s.code);c&&(Object.assign(s,c),i.unshift(c.map))}return{...s,map:Et(i,()=>null)}},"applyTransformersSync"),gr=u(async(n,e,A)=>{const i=[],s={code:e};for(const a of A){const c=await a(n,s.code);c&&(Object.assign(s,c),i.unshift(c.map))}return{...s,map:Et(i,()=>null)}},"applyTransformers"),Cr=Object.freeze({target:`node${process.versions.node}`,loader:"default"}),Er=/^--inspect(?:-brk|-port|-publish-uid|-wait)?(?:=|$)/,Br=process.execArgv.some(n=>Er.test(n)),Bt={...Cr,sourcemap:!0,sourcesContent:!!process.env.NODE_V8_COVERAGE||Br,minifyWhitespace:!0,keepNames:!0},dt=u(n=>{const e=n.sourcefile;if(e){const A=Z.extname(e.split("?")[0]);A?A===".cts"||A===".mts"?n.sourcefile=`${e.slice(0,-3)}ts`:A===".mjs"&&(n.sourcefile=`${e.slice(0,-3)}js`):n.sourcefile+=".js"}return A=>(A.map&&(n.sourcefile!==e&&(A.map=A.map.replace(JSON.stringify(n.sourcefile),JSON.stringify(e))),A.map=JSON.parse(A.map)),A)},"patchOptions"),wt=u(n=>{throw n.name="TransformError",delete n.errors,delete n.warnings,n},"formatEsbuildError"),dr=u((n,e,A)=>{const[i,s]=e.split("?"),a={};i.endsWith(".cjs")||i.endsWith(".cts")||(a["import.meta.url"]=JSON.stringify(Tt(i)+(s?`?${s}`:"")));const c={...Bt,format:"cjs",sourcefile:i,define:a,banner:"(()=>{",footer:"})()",platform:"node",...A},C=Je([n,JSON.stringify(c),Se,Te].join("-"));let Q=ne.get(C);return Q||(Q=Qr(e,n,[(g,f)=>{const r=dt(c);let m;try{m=Zt(f,c)}catch(K){throw wt(K)}return r(m)},(g,f)=>be(g,f,!0)]),ne.set(C,Q)),Q},"transformSync"),wr=u(async(n,e,A)=>{const i={...Bt,format:"esm",sourcefile:e,...A},s=Je([n,JSON.stringify(i),Se,Te].join("-"));let a=ne.get(s);return a||(a=await gr(e,n,[async(c,C)=>{const Q=dt(i);let g;try{g=await $t(C,i)}catch(f){throw wt(f)}return Q(g)},(c,C)=>be(c,C,!0)]),ne.set(s,a)),a},"transform");export{dr as a,be as b,wA as i,Ze as r,wr as t};
