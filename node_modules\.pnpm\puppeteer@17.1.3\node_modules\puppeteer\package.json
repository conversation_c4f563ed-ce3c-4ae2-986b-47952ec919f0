{"name": "puppeteer", "version": "17.1.3", "description": "A high-level API to control headless Chrome over the DevTools Protocol", "keywords": ["puppeteer", "chrome", "headless", "automation"], "type": "commonjs", "main": "./lib/cjs/puppeteer/puppeteer.js", "exports": {".": {"types": "./lib/types.d.ts", "import": "./lib/esm/puppeteer/puppeteer.js", "require": "./lib/cjs/puppeteer/puppeteer.js"}, "./*": {"import": "./*", "require": "./*"}}, "types": "lib/types.d.ts", "repository": "github:puppeteer/puppeteer", "engines": {"node": ">=14.1.0"}, "scripts": {"test": "cross-env MOZ_WEBRENDER=0 PUPPETEER_DEFERRED_PROMISE_DEBUG_TIMEOUT=20000 c8 --check-coverage --lines 93 node utils/mochaRunner/lib/main.js", "test:types": "tsd", "test:install": "scripts/test-install.sh", "test:firefox": "npm run test -- --test-suite firefox-headless", "test:chrome": "run-s test:chrome:*", "test:chrome:headless": "npm run test -- --test-suite chrome-headless", "test:chrome:headless-chrome": "npm run test -- --test-suite chrome-new-headless", "test:chrome:headful": "npm run test -- --test-suite chrome-headful", "prepublishOnly": "npm run build", "prepare": "node typescript-if-required.js && husky install", "lint": "run-s lint:prettier lint:eslint", "lint:prettier": "prettier --check .", "lint:eslint": "([ \"$CI\" = true ] && eslint --ext js --ext ts --quiet -f codeframe . || eslint --ext js --ext ts .)", "install": "node install.js", "generate:sources": "tsx utils/generate_sources.ts", "generate:artifacts": "tsx utils/generate_artifacts.ts", "generate:markdown": "tsx utils/generate_docs.ts", "format": "run-s format:*", "format:prettier": "prettier --write .", "format:eslint": "eslint --ext js --ext ts --fix .", "docs": "run-s build generate:markdown", "debug": "npm run build:dev && mocha --inspect-brk", "commitlint": "commitlint --from=HEAD~1", "clean": "rimraf lib && rimraf test/build", "check": "run-p check:*", "check:protocol-revision": "tsx scripts/ensure-correct-devtools-protocol-package", "check:pinned-deps": "tsx scripts/ensure-pinned-deps", "build": "npm run build:prod", "build:dev": "run-s generate:sources build:tsc:dev generate:artifacts", "build:prod": "run-s generate:sources build:tsc:prod generate:artifacts", "build:tsc:dev": "tsc -b test", "build:tsc:prod": "tsc -b tsconfig.lib.json"}, "files": ["lib", "install.js", "typescript-if-required.js", "!**/*.tsbuildinfo"], "author": "The Chromium Authors", "license": "Apache-2.0", "dependencies": {"cross-fetch": "3.1.5", "debug": "4.3.4", "devtools-protocol": "0.0.1036444", "extract-zip": "2.0.1", "https-proxy-agent": "5.0.1", "progress": "2.0.3", "proxy-from-env": "1.1.0", "rimraf": "3.0.2", "tar-fs": "2.1.1", "unbzip2-stream": "1.4.3", "ws": "8.8.1"}, "devDependencies": {"@commitlint/cli": "17.0.3", "@commitlint/config-conventional": "17.0.3", "@microsoft/api-documenter": "7.19.4", "@microsoft/api-extractor": "7.29.2", "@microsoft/api-extractor-model": "7.23.0", "@types/debug": "4.1.7", "@types/diff": "5.0.2", "@types/glob": "7.2.0", "@types/mime": "3.0.1", "@types/mocha": "9.1.1", "@types/node": "18.7.1", "@types/pixelmatch": "5.2.4", "@types/pngjs": "6.0.1", "@types/progress": "2.0.5", "@types/proxy-from-env": "1.0.1", "@types/rimraf": "3.0.2", "@types/semver": "7.3.11", "@types/sinon": "10.0.13", "@types/tar-fs": "2.0.1", "@types/unbzip2-stream": "1.4.0", "@types/ws": "8.5.3", "@typescript-eslint/eslint-plugin": "5.33.0", "@typescript-eslint/parser": "5.33.0", "c8": "7.12.0", "commonmark": "0.30.0", "cross-env": "7.0.3", "diff": "5.1.0", "esbuild": "0.15.5", "eslint": "8.21.0", "eslint-config-prettier": "8.5.0", "eslint-formatter-codeframe": "7.32.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-local": "1.0.0", "eslint-plugin-mocha": "10.1.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-tsdoc": "0.2.16", "eslint-plugin-unused-imports": "2.0.0", "esprima": "4.0.1", "expect": "25.2.7", "glob": "8.0.3", "gts": "4.0.0", "husky": "8.0.1", "jpeg-js": "0.4.4", "mime": "3.0.0", "minimist": "1.2.6", "mocha": "10.0.0", "ncp": "2.0.0", "npm-run-all": "4.1.5", "pixelmatch": "5.3.0", "pngjs": "6.0.0", "prettier": "2.7.1", "semver": "7.3.7", "sinon": "14.0.0", "source-map-support": "0.5.21", "text-diff": "1.0.1", "tsd": "0.22.0", "tsx": "3.8.2", "typescript": "4.7.4", "zod": "3.18.0"}}