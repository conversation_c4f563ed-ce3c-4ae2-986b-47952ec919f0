{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../../../src/node/install.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,KAAuB,MAAM,OAAO,CAAC;AAC5C,OAAO,WAAW,MAAM,UAAU,CAAC;AACnC,OAAO,GAAG,MAAM,KAAK,CAAC;AACtB,OAAO,SAAS,MAAM,iBAAiB,CAAC;AACxC,OAAO,EAAC,mBAAmB,EAAC,MAAM,iBAAiB,CAAC;AAEpD,OAAO,qBAA+C,MAAM,mBAAmB,CAAC;AAChF,OAAO,EAAC,cAAc,EAAC,MAAM,gBAAgB,CAAC;AAE9C;;GAEG;AACH,MAAM,iBAAiB,GAAG;IACxB,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,iBAAiB;CAClB,CAAC;AAEX;;GAEG;AACH,SAAS,UAAU,CAAC,KAAa;IAC/B,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QAC7C,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC;KACjD;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe;IACnC,MAAM,YAAY,GAChB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC5D,MAAM,OAAO,GAAG,UAAU,CACxB,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;QACnD,QAAQ,CACX,CAAC;IACF,MAAM,YAAY,GAChB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC5D,MAAM,cAAc,GAAI,SAA2B,CAAC,oBAAoB,CAAC;QACvE,OAAO;QACP,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,YAAY;KACnB,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,MAAM,WAAW,EAAE,CAAC;IACrC,MAAM,WAAW,CAAC,QAAQ,CAAC,CAAC;IAE5B,KAAK,UAAU,WAAW;QACxB,IAAI,OAAO,KAAK,QAAQ,EAAE;YACxB,OAAO,CACL,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;gBACrD,mBAAmB,CAAC,QAAQ,CAC7B,CAAC;SACH;aAAM,IAAI,OAAO,KAAK,SAAS,EAAE;YAC/B,SAA2B,CAAC,kBAAkB;gBAC7C,mBAAmB,CAAC,OAAO,CAAC;YAC9B,OAAO,wBAAwB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAC9C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;SACnD;IACH,CAAC;IAED,SAAS,WAAW,CAAC,QAAgB;QACnC,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE3D,oDAAoD;QACpD,IAAI,YAAY,CAAC,KAAK,EAAE;YACtB,WAAW,CACT,GAAG,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,YAAY,CAAC,UAAU,sBAAsB,CAC7F,CAAC;YACF,OAAO;SACR;QAED,8EAA8E;QAC9E,MAAM,eAAe,GACnB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC3E,MAAM,cAAc,GAClB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC1E,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAExD,IAAI,eAAe,EAAE;YACnB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;SAC9C;QACD,IAAI,cAAc,EAAE;YAClB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;SAC5C;QACD,IAAI,YAAY,EAAE;YAChB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;SACxC;QAED,SAAS,SAAS,CAAC,cAAwB;YACzC,WAAW,CACT,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,QAAQ,mBAAmB,YAAY,CAAC,UAAU,EAAE,CACpG,CAAC;YACF,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;gBAChD,OAAO,QAAQ,KAAK,YAAY,CAAC,QAAQ,CAAC;YAC5C,CAAC,CAAC,CAAC;YACH,MAAM,kBAAkB,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACvD,OAAO,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC;QACvC,CAAC;QAED,SAAS,OAAO,CAAC,KAAY;YAC3B,OAAO,CAAC,KAAK,CACX,2BAA2B,iBAAiB,CAAC,OAAO,CAAC,KAAK,QAAQ,gEAAgE,CACnI,CAAC;YACF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAED,IAAI,WAAW,GAAuB,IAAI,CAAC;QAC3C,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAC5B,SAAS,UAAU,CAAC,eAAuB,EAAE,UAAkB;YAC7D,IAAI,CAAC,WAAW,EAAE;gBAChB,WAAW,GAAG,IAAI,WAAW,CAC3B,eACE,iBAAiB,CAAC,OAAO,CAC3B,KAAK,QAAQ,MAAM,WAAW,CAAC,UAAU,CAAC,yBAAyB,EACnE;oBACE,QAAQ,EAAE,GAAG;oBACb,UAAU,EAAE,GAAG;oBACf,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,UAAU;iBAClB,CACF,CAAC;aACH;YACD,MAAM,KAAK,GAAG,eAAe,GAAG,mBAAmB,CAAC;YACpD,mBAAmB,GAAG,eAAe,CAAC;YACtC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,cAAc;aAClB,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC;aAC3C,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,cAAc,CAAC,cAAc,EAAE,CAAC;QACzC,CAAC,CAAC;aACD,IAAI,CAAC,SAAS,CAAC;aACf,KAAK,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAED,SAAS,WAAW,CAAC,KAAa;QAChC,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;QAC/B,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;IAC1C,CAAC;IAED,KAAK,UAAU,wBAAwB;QACrC,MAAM,kBAAkB,GACtB,+DAA+D,CAAC;QAElE,MAAM,QAAQ,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC;QAEpD,MAAM,cAAc,GAAmB,EAAE,CAAC;QAE1C,IAAI,QAAQ,EAAE;YACZ,MAAM,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAE3C,MAAM,YAAY,GAAG;gBACnB,GAAG,cAAc;gBACjB,WAAW,EAAE,cAAc,CAAC,QAAQ,KAAK,QAAQ;aACxB,CAAC;YAE5B,cAAc,CAAC,KAAK,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;YAC3D,cAAc,CAAC,kBAAkB,GAAG,KAAK,CAAC;SAC3C;QAED,MAAM,OAAO,GAAG,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtD,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,WAAW,CACT,kDAAkD,kBAAkB,EAAE,CACvE,CAAC;YACF,KAAK;iBACF,GAAG,CAAC,kBAAkB,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE;gBAC3C,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,UAAU,IAAI,GAAG,EAAE;oBACvC,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;iBAC7D;gBACD,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;oBACnB,IAAI,IAAI,KAAK,CAAC;gBAChB,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACf,IAAI;wBACF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAClC,OAAO,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;qBAC1C;oBAAC,MAAM;wBACN,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;qBACvD;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,UAAmB;IAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;IAC1D,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAE3E,sCAAsC;IACtC,IAAI,CAAC,eAAe,EAAE;QACpB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KACzB;AACH,CAAC"}