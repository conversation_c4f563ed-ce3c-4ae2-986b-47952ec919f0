// src/index.ts
function NeuePlusResolver(options = {}) {
  const { importStyle = "sass", prefix = "Ne" } = options;
  return {
    type: "component",
    resolve: (name) => {
      if (!name.startsWith(prefix)) return;
      const partialName = name.slice(prefix.length);
      const kebabCaseName = partialName.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
      console.log(kebabCaseName, "kebabCaseName");
      const path = `@neue-plus/components/${kebabCaseName}`;
      const stylePath = `@neue-plus/theme-chalk/src/${kebabCaseName}.${importStyle === "sass" ? "scss" : "css"}`;
      return {
        name,
        from: path,
        sideEffects: importStyle ? [stylePath] : void 0
      };
    }
  };
}
export {
  NeuePlusResolver
};
