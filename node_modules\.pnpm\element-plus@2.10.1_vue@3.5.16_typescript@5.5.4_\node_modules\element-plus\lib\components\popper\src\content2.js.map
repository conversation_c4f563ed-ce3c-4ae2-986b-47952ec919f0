{"version": 3, "file": "content2.js", "sources": ["../../../../../../packages/components/popper/src/content.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"contentRef\"\n    v-bind=\"contentAttrs\"\n    :style=\"contentStyle\"\n    :class=\"contentClass\"\n    tabindex=\"-1\"\n    @mouseenter=\"(e) => $emit('mouseenter', e)\"\n    @mouseleave=\"(e) => $emit('mouseleave', e)\"\n  >\n    <el-focus-trap\n      :trapped=\"trapped\"\n      :trap-on-focus-in=\"true\"\n      :focus-trap-el=\"contentRef\"\n      :focus-start-el=\"focusStartRef\"\n      @focus-after-trapped=\"onFocusAfterTrapped\"\n      @focus-after-released=\"onFocusAfterReleased\"\n      @focusin=\"onFocusInTrap\"\n      @focusout-prevented=\"onFocusoutPrevented\"\n      @release-requested=\"onReleaseRequested\"\n    >\n      <slot />\n    </el-focus-trap>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, onBeforeUnmount, onMounted, provide, unref, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { NOOP, isElement } from '@element-plus/utils'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport { formItemContextKey } from '@element-plus/components/form'\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants'\nimport { popperContentEmits, popperContentProps } from './content'\nimport {\n  usePopperContent,\n  usePopperContentDOM,\n  usePopperContentFocusTrap,\n} from './composables'\n\nimport type { WatchStopHandle } from 'vue'\n\ndefineOptions({\n  name: 'ElPopperContent',\n})\n\nconst emit = defineEmits(popperContentEmits)\n\nconst props = defineProps(popperContentProps)\n\nconst {\n  focusStartRef,\n  trapped,\n\n  onFocusAfterReleased,\n  onFocusAfterTrapped,\n  onFocusInTrap,\n  onFocusoutPrevented,\n  onReleaseRequested,\n} = usePopperContentFocusTrap(props, emit)\n\nconst { attributes, arrowRef, contentRef, styles, instanceRef, role, update } =\n  usePopperContent(props)\n\nconst {\n  ariaModal,\n  arrowStyle,\n  contentAttrs,\n  contentClass,\n  contentStyle,\n  updateZIndex,\n} = usePopperContentDOM(props, {\n  styles,\n  attributes,\n  role,\n})\n\nconst formItemContext = inject(formItemContextKey, undefined)\n\nprovide(POPPER_CONTENT_INJECTION_KEY, {\n  arrowStyle,\n  arrowRef,\n})\n\nif (formItemContext) {\n  // disallow auto-id from inside popper content\n  provide(formItemContextKey, {\n    ...formItemContext,\n    addInputId: NOOP,\n    removeInputId: NOOP,\n  })\n}\n\nlet triggerTargetAriaStopWatch: WatchStopHandle | undefined = undefined\n\nconst updatePopper = (shouldUpdateZIndex = true) => {\n  update()\n  shouldUpdateZIndex && updateZIndex()\n}\n\nconst togglePopperAlive = () => {\n  updatePopper(false)\n  if (props.visible && props.focusOnShow) {\n    trapped.value = true\n  } else if (props.visible === false) {\n    trapped.value = false\n  }\n}\n\nonMounted(() => {\n  watch(\n    () => props.triggerTargetEl,\n    (triggerTargetEl, prevTriggerTargetEl) => {\n      triggerTargetAriaStopWatch?.()\n      triggerTargetAriaStopWatch = undefined\n\n      const el = unref(triggerTargetEl || contentRef.value)\n      const prevEl = unref(prevTriggerTargetEl || contentRef.value)\n\n      if (isElement(el)) {\n        triggerTargetAriaStopWatch = watch(\n          [role, () => props.ariaLabel, ariaModal, () => props.id],\n          (watches) => {\n            ;['role', 'aria-label', 'aria-modal', 'id'].forEach((key, idx) => {\n              isNil(watches[idx])\n                ? el.removeAttribute(key)\n                : el.setAttribute(key, watches[idx]!)\n            })\n          },\n          { immediate: true }\n        )\n      }\n      if (prevEl !== el && isElement(prevEl)) {\n        ;['role', 'aria-label', 'aria-modal', 'id'].forEach((key) => {\n          prevEl.removeAttribute(key)\n        })\n      }\n    },\n    { immediate: true }\n  )\n\n  watch(() => props.visible, togglePopperAlive, { immediate: true })\n})\n\nonBeforeUnmount(() => {\n  triggerTargetAriaStopWatch?.()\n  triggerTargetAriaStopWatch = undefined\n})\n\ndefineExpose({\n  /**\n   * @description popper content element\n   */\n  popperContentRef: contentRef,\n  /**\n   * @description popperjs instance\n   */\n  popperInstanceRef: instanceRef,\n  /**\n   * @description method for updating popper\n   */\n  updatePopper,\n\n  /**\n   * @description content style\n   */\n  contentStyle,\n})\n</script>\n"], "names": ["usePopperContentFocusTrap", "usePopperContent", "usePopperContentDOM", "inject", "formItemContextKey", "provide", "POPPER_CONTENT_INJECTION_KEY", "NOOP", "onMounted", "watch", "unref", "isElement", "isNil", "onBeforeUnmount", "_openBlock", "_createElementBlock", "_mergeProps", "_unref", "_createVNode", "ElFocusTrap"], "mappings": ";;;;;;;;;;;;;;;;;uCA0Cc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAMA,IAAM,MAAA;AAAA,MACJ,aAAA;AAAA,MACA,OAAA;AAAA,MAEA,oBAAA;AAAA,MACA,mBAAA;AAAA,MACA,aAAA;AAAA,MACA,mBAAA;AAAA,MACA,kBAAA;AAAA,KACF,GAAIA,sCAA0B,CAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AAEzC,IAAM,MAAA,EAAE,UAAY,EAAA,QAAA,EAAU,UAAY,EAAA,MAAA,EAAQ,aAAa,IAAM,EAAA,MAAA,EACnE,GAAAC,2BAAA,CAAiB,KAAK,CAAA,CAAA;AAExB,IAAM,MAAA;AAAA,MACJ,SAAA;AAAA,MACA,UAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,KACF,GAAIC,kCAAoB,KAAO,EAAA;AAAA,MAC7B,MAAA;AAAA,MACA,UAAA;AAAA,MACA,IAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkBC,UAAO,CAAAC,4BAAA,EAAoB,KAAS,CAAA,CAAA,CAAA;AAE5D,IAAAC,WAAA,CAAQC,wCAA8B,EAAA;AAAA,MACpC,UAAA;AAAA,MACA,QAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,IAAI,eAAiB,EAAA;AAEnB,MAAAD,WAAA,CAAQD,4BAAoB,EAAA;AAAA,QAC1B,GAAG,eAAA;AAAA,QACH,UAAY,EAAAG,WAAA;AAAA,QACZ,aAAe,EAAAA,WAAA;AAAA,OAChB,CAAA,CAAA;AAAA,KACH;AAEA,IAAA,IAAI,0BAA0D,GAAA,KAAA,CAAA,CAAA;AAE9D,IAAM,MAAA,YAAA,GAAe,CAAC,kBAAA,GAAqB,IAAS,KAAA;AAClD,MAAO,MAAA,EAAA,CAAA;AACP,MAAA,kBAAA,IAAsB,YAAa,EAAA,CAAA;AAAA,KACrC,CAAA;AAEA,IAAA,MAAM,oBAAoB,MAAM;AAC9B,MAAA,YAAA,CAAa,KAAK,CAAA,CAAA;AAClB,MAAI,IAAA,KAAA,CAAM,OAAW,IAAA,KAAA,CAAM,WAAa,EAAA;AACtC,QAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA,CAAA;AAAA,OAClB,MAAA,IAAW,KAAM,CAAA,OAAA,KAAY,KAAO,EAAA;AAClC,QAAA,OAAA,CAAQ,KAAQ,GAAA,KAAA,CAAA;AAAA,OAClB;AAAA,KACF,CAAA;AAEA,IAAAC,aAAA,CAAU,MAAM;AACd,MAAAC,SAAA,CAAA,MAAA,KAAA,CAAA,eAAA,EAAA,CAAA,eAAA,EAAA,mBAAA,KAAA;AAAA,QACE,0BAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,0BAAA,EAAA,CAAA;AAAA,QACZ,0BAA0C,GAAA,KAAA,CAAA,CAAA;AACxC,QAA6B,MAAA,EAAA,GAAAC,SAAA,CAAA,eAAA,IAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAC7B,QAA6B,MAAA,MAAA,GAAAA,SAAA,CAAA,mBAAA,IAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAE7B,QAAA,IAAAC,eAAW,CAAA,EAAA,CAAA,EAAM;AACjB,UAAA,0BAA4C,GAAAF,SAAA,CAAA,CAAA,IAAA,EAAA,MAAA,KAAA,CAAA,SAAgB,EAAA,SAAA,EAAA,MAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,KAAA;AAG1D,YAA6B,CAAA,MAAA,EAAA,YAAA,EAAA,YAAA,EAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA,GAAA,EAAA,GAAA,KAAA;AAAA,cAC3BG,2BAAa,CAAA,GAAA,CAAA,CAAM,qBAAsB,CAAA,GAAA,CAAA,kBAAc,CAAA,GAAA,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,aAAA,CACvD,CAAC;AACC,WAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAC,SAAC;AACA,QAAA,IAAA,MAAA,KAAA,EAAc,IAAAD,eAAI,CAAA,MACX,CAAA,EAAA;AAER,UACH,CAAA,MAAA,EAAA,YAAA,EAAA,YAAA,EAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA;AAAA,YACA,sBAAkB,CAAA,GAAA,CAAA,CAAA;AAAA,WACpB,CAAA,CAAA;AAAA,SACF;AACA,OAAA,EAAA,EAAA,SAAe,EAAA,IAAA,EAAA,CAAA,CAAA;AACb,MAAAF,SAAA,CAAA,MAAA,KAAA,CAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAC,KAAA,CAAA,CAAA;AACC,IAAAI,mBAAA,CAAA;AAA0B,MAAA,0BAC3B,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,0BAAA,EAAA,CAAA;AAAA,MACH,0BAAA,GAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,IACA,MAAE;AAAgB,MACpB,gBAAA,EAAA,UAAA;AAEA,MAAA,iBAAkB,EAAA;AAA+C,MAClE,YAAA;AAED,MAAA,YAAA;AACE,KAA6B,CAAA,CAAA;AAC7B,IAA6B,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAC9B,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAAC,cAAA,CAAA;AAED,QAAa,OAAA,EAAA,YAAA;AAAA,QAAA,GAAA,EAAA,UAAA;AAAA,OAAA,EAAAC,SAAA,CAAA,YAAA,CAAA,EAAA;AAAA,QAAA,KAAA,EAAAA,SAAA,CAAA,YAAA,CAAA;AAAA,QAIO,KAAA,EAAAA,SAAA,CAAA,YAAA,CAAA;AAAA,QAAA,QAAA,EAAA,IAAA;AAAA,QAAA,YAAA,EAAA,CAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,YAAA,EAAA,CAAA,CAAA;AAAA,QAAA,YAAA,EAAA,CAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,YAAA,EAAA,CAAA,CAAA;AAAA,OAIC,CAAA,EAAA;AAAA,QAAAC,eAAA,CAAAD,SAAA,CAAAE,oBAAA,CAAA,EAAA;AAAA,UAAA,OAAA,EAAAF,SAAA,CAAA,OAAA,CAAA;AAAA,UAAA,kBAAA,EAAA,IAAA;AAAA,UAInB,eAAA,EAAAA,SAAA,CAAA,UAAA,CAAA;AAAA,UAAA,gBAAA,EAAAA,SAAA,CAAA,aAAA,CAAA;AAAA,UAAA,mBAAA,EAAAA,SAAA,CAAA,mBAAA,CAAA;AAAA,UAAA,oBAAA,EAAAA,SAAA,CAAA,oBAAA,CAAA;AAAA,UAKA,SAAA,EAAAA,SAAA,CAAA,aAAA,CAAA;AAAA,UACD,mBAAA,EAAAA,SAAA,CAAA,mBAAA,CAAA;;;;;;;;;;;;;;;;"}