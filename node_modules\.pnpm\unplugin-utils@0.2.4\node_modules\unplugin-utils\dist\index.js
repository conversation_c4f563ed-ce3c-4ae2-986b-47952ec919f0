import { isAbsolute, join, resolve } from "pathe";
import pm from "picomatch";

//#region src/path.ts
function normalizePath(filename) {
	return filename.replaceAll("\\", "/");
}

//#endregion
//#region src/utils.ts
const isArray = Array.isArray;
function toArray(thing) {
	if (isArray(thing)) return thing;
	if (thing == null) return [];
	return [thing];
}

//#endregion
//#region src/filter.ts
const escapeMark = "[_#EsCaPe#_]";
function getMatcherString(id, resolutionBase) {
	if (resolutionBase === false || isAbsolute(id) || id.startsWith("**")) return normalizePath(id);
	const basePath = normalizePath(resolve(resolutionBase || "")).replaceAll(/[-^$*+?.()|[\]{}]/g, `${escapeMark}$&`);
	return join(basePath, normalizePath(id)).replaceAll(escapeMark, "\\");
}
function createFilter(include, exclude, options) {
	const resolutionBase = options && options.resolve;
	const getMatcher = (id) => id instanceof RegExp ? id : { test: (what) => {
		const pattern = getMatcherString(id, resolutionBase);
		const fn = pm(pattern, { dot: true });
		const result = fn(what);
		return result;
	} };
	const includeMatchers = toArray(include).map(getMatcher);
	const excludeMatchers = toArray(exclude).map(getMatcher);
	if (!includeMatchers.length && !excludeMatchers.length) return (id) => typeof id === "string" && !id.includes("\0");
	return function result(id) {
		if (typeof id !== "string") return false;
		if (id.includes("\0")) return false;
		const pathId = normalizePath(id);
		for (const matcher of excludeMatchers) {
			if (matcher instanceof RegExp) matcher.lastIndex = 0;
			if (matcher.test(pathId)) return false;
		}
		for (const matcher of includeMatchers) {
			if (matcher instanceof RegExp) matcher.lastIndex = 0;
			if (matcher.test(pathId)) return true;
		}
		return !includeMatchers.length;
	};
}

//#endregion
export { createFilter, normalizePath, toArray };