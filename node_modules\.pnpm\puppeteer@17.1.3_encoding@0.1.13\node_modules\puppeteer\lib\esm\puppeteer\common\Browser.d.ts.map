{"version": 3, "file": "Browser.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/Browser.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAa,UAAU,EAA0B,MAAM,iBAAiB,CAAC;AAChF,OAAO,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAE/C,OAAO,EAAC,IAAI,EAAC,MAAM,WAAW,CAAC;AAC/B,OAAO,EAAC,QAAQ,EAAC,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAC,MAAM,EAAC,MAAM,aAAa,CAAC;AAEnC,OAAO,EAAC,aAAa,EAA6B,MAAM,oBAAoB,CAAC;AAI7E;;;;GAIG;AACH,MAAM,WAAW,qBAAqB;IACpC;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;CAC5B;AAED;;GAEG;AACH,oBAAY,oBAAoB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAE9D;;GAEG;AACH,oBAAY,oBAAoB,GAAG,CACjC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,KAC/B,OAAO,CAAC;AAEb;;GAEG;AACH,oBAAY,oBAAoB,GAAG,CACjC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,KAC/B,OAAO,CAAC;AA4Bb;;GAEG;AACH,oBAAY,UAAU,GAClB,aAAa,GACb,MAAM,GACN,eAAe,GACf,QAAQ,GACR,YAAY,GACZ,iBAAiB,GACjB,sBAAsB,GACtB,eAAe,GACf,WAAW,GACX,cAAc,GACd,sBAAsB,GACtB,gBAAgB,GAChB,iBAAiB,GACjB,iBAAiB,GACjB,oBAAoB,GACpB,gBAAgB,GAChB,YAAY,CAAC;AAEjB;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;;;GAIG;AACH,0BAAkB,oBAAoB;IACpC;;;;;;;OAOG;IACH,YAAY,iBAAiB;IAE7B;;;;;;OAMG;IACH,aAAa,kBAAkB;IAE/B;;;;;;;;;;OAUG;IACH,aAAa,kBAAkB;IAC/B;;;;;;;OAOG;IACH,eAAe,oBAAoB;CACpC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AACH,qBAAa,OAAQ,SAAQ,YAAY;;IACvC;;OAEG;WACU,OAAO,CAClB,OAAO,EAAE,SAAS,GAAG,QAAQ,GAAG,SAAS,EACzC,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,MAAM,EAAE,EACpB,iBAAiB,EAAE,OAAO,EAC1B,eAAe,CAAC,EAAE,QAAQ,GAAG,IAAI,EACjC,OAAO,CAAC,EAAE,YAAY,EACtB,aAAa,CAAC,EAAE,oBAAoB,EACpC,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,oBAAoB,CAAC,EAAE,oBAAoB,GAC1C,OAAO,CAAC,OAAO,CAAC;IA2BnB;;OAEG;IACH,IAAI,QAAQ,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAElC;IAED;;OAEG;gBAED,OAAO,EAAE,QAAQ,GAAG,SAAS,GAAG,SAAS,EACzC,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,MAAM,EAAE,EACpB,iBAAiB,EAAE,OAAO,EAC1B,eAAe,CAAC,EAAE,QAAQ,GAAG,IAAI,EACjC,OAAO,CAAC,EAAE,YAAY,EACtB,aAAa,CAAC,EAAE,oBAAoB,EACpC,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,oBAAoB,CAAC,EAAE,oBAAoB;IA2C7C;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAwB9B;;OAEG;IACH,OAAO,IAAI,IAAI;IAuBf;;;OAGG;IACH,OAAO,IAAI,YAAY,GAAG,IAAI;IAI9B;;OAEG;IACH,cAAc,IAAI,aAAa;IAgB/B;;OAEG;IACH,wBAAwB,IAAI,oBAAoB,GAAG,SAAS;IAI5D;;;;;;;;;;;;;;;;;OAiBG;IACG,6BAA6B,CACjC,OAAO,GAAE,qBAA0B,GAClC,OAAO,CAAC,cAAc,CAAC;IAmB1B;;;OAGG;IACH,eAAe,IAAI,cAAc,EAAE;IAInC;;OAEG;IACH,qBAAqB,IAAI,cAAc;IAIvC;;OAEG;IACG,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAoFxD;;;;;;;;;;;;;;;;OAgBG;IACH,UAAU,IAAI,MAAM;IAIpB;;;OAGG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAI9B;;OAEG;IACG,oBAAoB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAsB7D;;;OAGG;IACH,OAAO,IAAI,MAAM,EAAE;IAQnB;;OAEG;IACH,MAAM,IAAI,MAAM;IAUhB;;;;;;;;;;;;;;;;OAgBG;IACG,aAAa,CACjB,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,EACpD,OAAO,GAAE,oBAAyB,GACjC,OAAO,CAAC,MAAM,CAAC;IA4BlB;;;;;;;;OAQG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;IAY9B;;;;;;;;;OASG;IACG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAKhC;;;OAGG;IACG,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IAKlC;;;OAGG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAK5B;;;;OAIG;IACH,UAAU,IAAI,IAAI;IAKlB;;OAEG;IACH,WAAW,IAAI,OAAO;CAOvB;AACD;;GAEG;AACH,0BAAkB,2BAA2B;IAC3C;;;OAGG;IACH,aAAa,kBAAkB;IAE/B;;;;;;;OAOG;IACH,aAAa,kBAAkB;IAC/B;;;OAGG;IACH,eAAe,oBAAoB;CACpC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,qBAAa,cAAe,SAAQ,YAAY;;IAK9C;;OAEG;gBACS,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,MAAM;IAOxE;;OAEG;IACH,OAAO,IAAI,MAAM,EAAE;IAMnB;;;;;;;;;;;;;;;;;;;OAmBG;IACH,aAAa,CACX,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,EACpD,OAAO,GAAE;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAM,GAC/B,OAAO,CAAC,MAAM,CAAC;IAMlB;;;;;;OAMG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;IAqB9B;;;;;;OAMG;IACH,WAAW,IAAI,OAAO;IAItB;;;;;;;;;;;;;OAaG;IACG,mBAAmB,CACvB,MAAM,EAAE,MAAM,EACd,WAAW,EAAE,UAAU,EAAE,GACxB,OAAO,CAAC,IAAI,CAAC;IAgBhB;;;;;;;;;;;OAWG;IACG,wBAAwB,IAAI,OAAO,CAAC,IAAI,CAAC;IAM/C;;OAEG;IACH,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIxB;;OAEG;IACH,OAAO,IAAI,OAAO;IAIlB;;;;;;OAMG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAI7B"}