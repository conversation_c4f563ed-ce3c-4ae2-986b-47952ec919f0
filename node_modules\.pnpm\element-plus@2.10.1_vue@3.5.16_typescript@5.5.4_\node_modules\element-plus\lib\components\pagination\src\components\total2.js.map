{"version": 3, "file": "total2.js", "sources": ["../../../../../../../packages/components/pagination/src/components/total.vue"], "sourcesContent": ["<template>\n  <span :class=\"ns.e('total')\" :disabled=\"disabled\">\n    {{\n      t('el.pagination.total', {\n        total,\n      })\n    }}\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { usePagination } from '../usePagination'\nimport { paginationTotalProps } from './total'\n\nconst { t } = useLocale()\nconst ns = useNamespace('pagination')\nconst { disabled } = usePagination()\n\ndefineOptions({\n  name: 'ElPaginationTotal',\n})\n\ndefineProps(paginationTotalProps)\n</script>\n"], "names": ["useLocale", "useNamespace", "usePagination"], "mappings": ";;;;;;;;;;;uCAmBc,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;AANA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIA,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,YAAY,CAAA,CAAA;AACpC,IAAM,MAAA,EAAE,QAAS,EAAA,GAAIC,2BAAc,EAAA,CAAA;;;;;;;;;;;;;;;"}