{"version": 3, "file": "QueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/QueryHandler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,+DAAkD;AAClD,yDAAiD;AACjD,yCAAiC;AACjC,yDAI4B;AAkD5B,SAAS,6BAA6B,CACpC,OAA2B;IAE3B,MAAM,eAAe,GAAyB,EAAE,CAAC;IAEjD,IAAI,OAAO,CAAC,QAAQ,EAAE;QACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,eAAe,CAAC,QAAQ,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC3C,IAAI,aAAa,EAAE;gBACjB,OAAO,aAAa,CAAC;aACtB;YACD,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QACF,eAAe,CAAC,OAAO,GAAG,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;YACpE,IAAI,KAAY,CAAC;YACjB,IAAI,OAAwC,CAAC;YAC7C,IAAI,cAAc,YAAY,gBAAK,EAAE;gBACnC,KAAK,GAAG,cAAc,CAAC;aACxB;iBAAM;gBACL,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;gBAC7B,OAAO,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,kCAAe,CAAC,CAAC,WAAW,CACvD,cAAc,CACf,CAAC;aACH;YACD,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,kCAAe,CAAC,CAAC,sBAAsB,CACvE,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,OAAO,CACR,CAAC;YACF,IAAI,OAAO,EAAE;gBACX,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;aACzB;YACD,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,IAAI,CAAC;aACb;YACD,IAAI,CAAC,CAAC,MAAM,YAAY,gCAAa,CAAC,EAAE;gBACtC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC;aACb;YACD,OAAO,KAAK,CAAC,MAAM,CAAC,6BAAU,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC,CAAC;KACH;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE;QACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,eAAe,CAAC,QAAQ,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE;gBAC1C,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC3C,IAAI,aAAa,EAAE;oBACjB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;iBAC5B;aACF;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;KACH;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,MAAM,cAAc,GAAG,6BAA6B,CAAC;IACnD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,IAAI,CAAC,CAAC,eAAe,IAAI,OAAO,CAAC,EAAE;YACjC,MAAM,IAAI,KAAK,CACb,sDAAsD,OAAO,CAAC,QAAQ,GAAG,CAC1E,CAAC;SACH;QACD,OACE,OACD,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IACD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,IAAI,CAAC,CAAC,kBAAkB,IAAI,OAAO,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CACb,yDAAyD,OAAO,CAAC,QAAQ,GAAG,CAC7E,CAAC;SACH;QACD,OAAO;YACL,GACE,OAGD,CAAC,gBAAgB,CAAC,QAAQ,CAAC;SAC7B,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,aAAa,GAAG,6BAA6B,CAAC;IAClD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,IAAI,KAAK,GAAgB,IAAI,CAAC;QAC9B,MAAM,MAAM,GAAG,CAAC,IAAU,EAAE,EAAE;YAC5B,MAAM,IAAI,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;YACtE,GAAG;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAA0B,CAAC;gBACpD,IAAI,WAAW,CAAC,UAAU,EAAE;oBAC1B,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;iBAChC;gBACD,IAAI,WAAW,YAAY,UAAU,EAAE;oBACrC,SAAS;iBACV;gBACD,IAAI,WAAW,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACnE,KAAK,GAAG,WAAW,CAAC;iBACrB;aACF,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;QACtC,CAAC,CAAC;QACF,IAAI,OAAO,YAAY,QAAQ,EAAE;YAC/B,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC;SACnC;QACD,MAAM,CAAC,OAAO,CAAC,CAAC;QAChB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,MAAM,MAAM,GAAW,EAAE,CAAC;QAC1B,MAAM,OAAO,GAAG,CAAC,IAAU,EAAE,EAAE;YAC7B,MAAM,IAAI,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;YACtE,GAAG;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAA0B,CAAC;gBACpD,IAAI,WAAW,CAAC,UAAU,EAAE;oBAC1B,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;iBACjC;gBACD,IAAI,WAAW,YAAY,UAAU,EAAE;oBACrC,SAAS;iBACV;gBACD,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACzD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC1B;aACF,QAAQ,IAAI,CAAC,QAAQ,EAAE,EAAE;QAC5B,CAAC,CAAC;QACF,IAAI,OAAO,YAAY,QAAQ,EAAE;YAC/B,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC;SACnC;QACD,OAAO,CAAC,OAAO,CAAC,CAAC;QACjB,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,YAAY,GAAG,6BAA6B,CAAC;IACjD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;QAC9C,MAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CACzB,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,WAAW,CAAC,uBAAuB,CACpC,CAAC;QACF,OAAO,MAAM,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;QAC9C,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAC3B,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,WAAW,CAAC,0BAA0B,CACvC,CAAC;QACF,MAAM,KAAK,GAAW,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC;QACT,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE;YACtC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAC,CAAC;AAOH,MAAM,uBAAuB,GAAG,IAAI,GAAG,CAAiC;IACtE,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,iCAAW,EAAC,CAAC;IAChC,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,aAAa,EAAC,CAAC;IACpC,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,YAAY,EAAC,CAAC;CACnC,CAAC,CAAC;AACH,MAAM,cAAc,GAAG,IAAI,GAAG,EAAkC,CAAC;AAEjE;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,SAAgB,0BAA0B,CACxC,IAAY,EACZ,OAA2B;IAE3B,IAAI,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,kBAAkB,CAAC,CAAC;KACnE;IACD,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,kBAAkB,CAAC,CAAC;KAC1E;IAED,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;KACzE;IAED,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC,OAAO,EAAE,6BAA6B,CAAC,OAAO,CAAC,EAAC,CAAC,CAAC;AAC9E,CAAC;AAjBD,gEAiBC;AAED;;;;GAIG;AACH,SAAgB,4BAA4B,CAAC,IAAY;IACvD,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC;AAFD,oEAEC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB;IACrC,OAAO,CAAC,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;AACpC,CAAC;AAFD,0DAEC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB;IACtC,cAAc,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAFD,4DAEC;AAED,MAAM,uBAAuB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAE3C;;GAEG;AACH,SAAgB,0BAA0B,CAAC,QAAgB;IAIzD,KAAK,MAAM,UAAU,IAAI,CAAC,cAAc,EAAE,uBAAuB,CAAC,EAAE;QAClE,KAAK,MAAM,CACT,IAAI,EACJ,EAAC,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAC,EAC3C,IAAI,UAAU,EAAE;YACf,KAAK,MAAM,SAAS,IAAI,uBAAuB,EAAE;gBAC/C,MAAM,MAAM,GAAG,GAAG,IAAI,GAAG,SAAS,EAAE,CAAC;gBACrC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;oBAC/B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACzC,IAAI,iBAAiB,EAAE;wBACrB,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;qBACxC;oBACD,OAAO,EAAC,eAAe,EAAE,QAAQ,EAAE,YAAY,EAAC,CAAC;iBAClD;aACF;SACF;KACF;IACD,OAAO,EAAC,eAAe,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,EAAC,CAAC;AACnE,CAAC;AAtBD,gEAsBC"}