#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/tsup@8.5.0_@microsoft+api-extractor@7.52.5_@types+node@22.9.0__jiti@2.4.2_postcss@8.5.4_tsx@4_tqbvg2u2vpouxzl7n572jgqdye/node_modules/tsup/dist/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/tsup@8.5.0_@microsoft+api-extractor@7.52.5_@types+node@22.9.0__jiti@2.4.2_postcss@8.5.4_tsx@4_tqbvg2u2vpouxzl7n572jgqdye/node_modules/tsup/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/tsup@8.5.0_@microsoft+api-extractor@7.52.5_@types+node@22.9.0__jiti@2.4.2_postcss@8.5.4_tsx@4_tqbvg2u2vpouxzl7n572jgqdye/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/tsup@8.5.0_@microsoft+api-extractor@7.52.5_@types+node@22.9.0__jiti@2.4.2_postcss@8.5.4_tsx@4_tqbvg2u2vpouxzl7n572jgqdye/node_modules/tsup/dist/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/tsup@8.5.0_@microsoft+api-extractor@7.52.5_@types+node@22.9.0__jiti@2.4.2_postcss@8.5.4_tsx@4_tqbvg2u2vpouxzl7n572jgqdye/node_modules/tsup/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/tsup@8.5.0_@microsoft+api-extractor@7.52.5_@types+node@22.9.0__jiti@2.4.2_postcss@8.5.4_tsx@4_tqbvg2u2vpouxzl7n572jgqdye/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../tsup/dist/cli-default.js" "$@"
else
  exec node  "$basedir/../tsup/dist/cli-default.js" "$@"
fi
