{"version": 3, "file": "useContainer.js", "sources": ["../../../../../../../packages/components/splitter/src/hooks/useContainer.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { useElementSize } from '@vueuse/core'\nimport type { Ref } from 'vue'\n\nexport function useContainer(layout: Ref<'horizontal' | 'vertical'>) {\n  const containerEl = ref<HTMLDivElement>()\n  const { width, height } = useElementSize(containerEl)\n\n  const containerSize = computed(() => {\n    return layout.value === 'horizontal' ? width.value : height.value\n  })\n\n  return { containerEl, containerSize }\n}\n"], "names": ["ref", "useElementSize", "computed"], "mappings": ";;;;;;;AAEO,SAAS,YAAY,CAAC,MAAM,EAAE;AACrC,EAAE,MAAM,WAAW,GAAGA,OAAG,EAAE,CAAC;AAC5B,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAGC,mBAAc,CAAC,WAAW,CAAC,CAAC;AACxD,EAAE,MAAM,aAAa,GAAGC,YAAQ,CAAC,MAAM;AACvC,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,YAAY,GAAG,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACtE,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;AACxC;;;;"}