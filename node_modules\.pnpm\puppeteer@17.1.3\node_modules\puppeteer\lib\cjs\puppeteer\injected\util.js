"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFunction = void 0;
const createdFunctions = new Map();
/**
 * Creates a function from a string.
 */
const createFunction = (functionValue) => {
    let fn = createdFunctions.get(functionValue);
    if (fn) {
        return fn;
    }
    fn = new Function(`return ${functionValue}`)();
    createdFunctions.set(functionValue, fn);
    return fn;
};
exports.createFunction = createFunction;
//# sourceMappingURL=util.js.map