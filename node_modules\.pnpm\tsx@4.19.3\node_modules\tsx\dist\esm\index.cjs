"use strict";var E=Object.defineProperty;var u=(e,t)=>E(e,"name",{value:t,configurable:!0});var S=require("node:worker_threads"),f=require("../node-features-roYmp9jK.cjs"),F=require("../register-CVZT-Tul.cjs");require("../get-pipe-path-BoR10qr8.cjs"),require("node:module");var m=require("node:path"),h=require("node:url");require("get-tsconfig");var c=require("../register-DCnOAxY2.cjs"),U=require("node:fs");require("esbuild"),require("node:crypto");var l=require("../index-B4SIRlEU.cjs"),k=require("../client-D6NvIMSC.cjs");require("../require-DBpSESet.cjs");var L=require("node:fs/promises");require("module"),require("../temporary-directory-B83uKxJF.cjs"),require("node:os"),require("node:net");const p={active:!0},J=u(async e=>{if(!e)throw new Error(`tsx must be loaded with --import instead of --loader
The --loader flag was deprecated in Node v20.6.0 and v18.19.0`);p.namespace=e.namespace,e.tsconfig!==!1&&c.loadTsconfig(e.tsconfig??process.env.TSX_TSCONFIG_PATH),e.port&&(p.port=e.port,e.port.on("message",t=>{t==="deactivate"&&(p.active=!1,e.port.postMessage({type:"deactivated"}))}))},"initialize"),N=u(()=>(c.loadTsconfig(process.env.TSX_TSCONFIG_PATH),"process.setSourceMapsEnabled(true);"),"globalPreload"),g=new Map,O=u(async e=>{if(g.has(e))return g.get(e);if(!await U.promises.access(e).then(()=>!0,()=>!1)){g.set(e,void 0);return}const s=await U.promises.readFile(e,"utf8");try{const r=JSON.parse(s);return g.set(e,r),r}catch{throw new Error(`Error parsing: ${e}`)}},"readPackageJson"),M=u(async e=>{let t=new URL("package.json",e);for(;!t.pathname.endsWith("/node_modules/package.json");){const s=h.fileURLToPath(t),r=await O(s);if(r)return r;const a=t;if(t=new URL("../package.json",t),t.pathname===a.pathname)break}},"findPackageJson"),b=u(async e=>(await M(e))?.type??"commonjs","getPackageType"),j=u(e=>{[e]=e.split("?");const t=m.extname(e);if(t===".mts")return"module";if(t===".cts")return"commonjs"},"getFormatFromExtension"),W=u(e=>{const t=j(e);if(t)return t;if(c.tsExtensionsPattern.test(e))return b(e)},"getFormatFromFileUrl"),P="tsx-namespace=",y=u(e=>{const t=e.indexOf(P);if(t===-1)return;const s=e[t-1];if(s!=="?"&&s!=="&")return;const r=t+P.length,a=e.indexOf("&",r);return a===-1?e.slice(r):e.slice(r,a)},"getNamespace"),R=f.isFeatureSupported(f.importAttributes)?"importAttributes":"importAssertions",A=u(async(e,t,s)=>{if(!p.active)return s(e,t);const r=y(e);if(p.namespace&&p.namespace!==r)return s(e,t);if(p.port){const n=new URL(e);n.searchParams.delete("tsx-namespace"),p.port.postMessage({type:"load",url:n.toString()})}k.parent.send&&k.parent.send({type:"dependency",path:e}),c.isJsonPattern.test(e)&&(t[R]||(t[R]={}),t[R].type="json");const a=await s(e,t),o=e.startsWith(c.fileUrlPrefix)?h.fileURLToPath(e):e;if(a.format==="commonjs"&&f.isFeatureSupported(f.esmLoadReadFile)&&a.responseURL?.startsWith("file:")&&!o.endsWith(".cjs")){const n=await L.readFile(new URL(e),"utf8");if(!o.endsWith(".js")||l.isESM(n)){const d=l.transformSync(n,o,{tsconfigRaw:c.fileMatcher?.(o)}),q=r?`${o}?namespace=${encodeURIComponent(r)}`:o;return a.responseURL=`data:text/javascript,${encodeURIComponent(d.code)}?filePath=${encodeURIComponent(q)}`,a}}if(!a.source)return a;const i=a.source.toString();if(a.format==="json"||c.tsExtensionsPattern.test(e)){const n=await l.transform(i,o,{tsconfigRaw:m.isAbsolute(o)?c.fileMatcher?.(o):void 0});return{format:"module",source:c.inlineSourceMap(n)}}if(a.format==="module"){const n=l.transformDynamicImport(o,i);n&&(a.source=c.inlineSourceMap(n))}return a},"load"),v=u(e=>{if(e.url)return e.url;const t=e.message.match(/^Cannot find module '([^']+)'/);if(t){const[,r]=t;return r}const s=e.message.match(/^Cannot find package '([^']+)'/);if(s){const[,r]=s;if(!m.isAbsolute(r))return;const a=h.pathToFileURL(r);if(a.pathname.endsWith("/")&&(a.pathname+="package.json"),a.pathname.endsWith("/package.json")){const o=l.readJsonFile(a);if(o?.main)return new URL(o.main,a).toString()}else return a.toString()}},"getMissingPathFromNotFound"),w=u(async(e,t,s,r)=>{const a=c.mapTsExtensions(e);if(!a)return;let o;for(const i of a)try{return await s(i,t)}catch(n){const{code:d}=n;if(d!=="ERR_MODULE_NOT_FOUND"&&d!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw n;o=n}if(r)throw o},"resolveExtensions"),I=u(async(e,t,s)=>{if((e.startsWith(c.fileUrlPrefix)||c.isRelativePath(e))&&(c.tsExtensionsPattern.test(t.parentURL)||c.allowJs)){const r=await w(e,t,s);if(r)return r}try{return await s(e,t)}catch(r){if(r instanceof Error){const a=r;if(a.code==="ERR_MODULE_NOT_FOUND"){const o=v(a);if(o){const i=await w(o,t,s);if(i)return i}}}throw r}},"resolveBase"),T=u(async(e,t,s)=>{if((e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),c.isDirectoryPattern.test(e)){const r=new URL(e,t.parentURL);return r.pathname=m.join(r.pathname,"index"),await w(r.toString(),t,s,!0)}try{return await I(e,t,s)}catch(r){if(r instanceof Error){const a=r;if(a.code==="ERR_UNSUPPORTED_DIR_IMPORT"){const o=v(a);if(o)try{return await w(`${o}/index`,t,s,!0)}catch(i){const n=i,{message:d}=n;throw n.message=n.message.replace(`${"/index".replace("/",m.sep)}'`,"'"),n.stack=n.stack.replace(d,n.message),n}}}throw r}},"resolveDirectory"),D=u(async(e,t,s)=>{if(!c.requestAcceptsQuery(e)&&c.tsconfigPathsMatcher&&!t.parentURL?.includes("/node_modules/")){const r=c.tsconfigPathsMatcher(e);for(const a of r)try{return await T(h.pathToFileURL(a).toString(),t,s)}catch{}}return T(e,t,s)},"resolveTsPaths"),_="tsx://",C=u(async(e,t,s)=>{if(!p.active||e.startsWith("node:"))return s(e,t);let r=y(e)??(t.parentURL&&y(t.parentURL));if(p.namespace){let n;if(e.startsWith(_)){try{n=JSON.parse(e.slice(_.length))}catch{}n?.namespace&&(r=n.namespace)}if(p.namespace!==r)return s(e,t);n&&(e=n.specifier,t.parentURL=n.parentURL)}const[a,o]=e.split("?"),i=await D(a,t,s);return i.format==="builtin"||(!i.format&&i.url.startsWith(c.fileUrlPrefix)&&(i.format=await W(i.url)),o&&(i.url+=`?${o}`),r&&!i.url.includes(P)&&(i.url+=(i.url.includes("?")?"&":"?")+P+r)),i},"resolve");f.isFeatureSupported(f.moduleRegister)&&S.isMainThread&&F.register(),exports.globalPreload=N,exports.initialize=J,exports.load=A,exports.resolve=C;
