{"version": 3, "file": "splitter2.js", "sources": ["../../../../../../packages/components/splitter/src/splitter.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { getCurrentInstance, provide, reactive, toRef, watch } from 'vue'\nimport { useNamespace, useOrderedChildren } from '@element-plus/hooks'\nimport { useContainer, useResize, useSize } from './hooks'\nimport { splitterProps } from './splitter'\nimport { type PanelItemState, splitterRootContextKey } from './type'\n\nconst ns = useNamespace('splitter')\n\ndefineOptions({\n  name: 'ElSplitter',\n})\n\nconst emits = defineEmits<{\n  (e: 'resizeStart', index: number, sizes: number[]): void\n  (e: 'resize', index: number, sizes: number[]): void\n  (e: 'resizeEnd', index: number, sizes: number[]): void\n}>()\n\nconst props = defineProps(splitterProps)\n\nconst { containerEl, containerSize } = useContainer(toRef(props, 'layout'))\n\nconst {\n  removeChild: unregisterPanel,\n  children: panels,\n  addChild: sortPanel,\n} = useOrderedChildren<PanelItemState>(getCurrentInstance()!, 'ElSplitterPanel')\n\nwatch(panels, () => {\n  panels.value.forEach((instance: PanelItemState, index: number) => {\n    instance.setIndex(index)\n  })\n})\n\nconst { percentSizes, pxSizes } = useSize(panels, containerSize)\n\nconst { onMoveStart, onMoving, onMoveEnd, onCollapse, movingIndex } = useResize(\n  panels,\n  containerSize,\n  pxSizes\n)\n\nconst onResizeStart = (index: number) => {\n  onMoveStart(index)\n  emits('resizeStart', index, pxSizes.value)\n}\n\nconst onResize = (index: number, offset: number) => {\n  onMoving(index, offset)\n  emits('resize', index, pxSizes.value)\n}\n\nconst onResizeEnd = (index: number) => {\n  onMoveEnd()\n  emits('resizeEnd', index, pxSizes.value)\n}\n\nprovide(\n  splitterRootContextKey,\n  reactive({\n    panels,\n    percentSizes,\n    pxSizes,\n    layout: props.layout,\n    movingIndex,\n    containerSize,\n    onMoveStart: onResizeStart,\n    onMoving: onResize,\n    onMoveEnd: onResizeEnd,\n    onCollapse,\n    registerPanel: (panel: PanelItemState) => {\n      panels.value.push(panel)\n    },\n    sortPanel,\n    unregisterPanel,\n  })\n)\n</script>\n\n<template>\n  <div ref=\"containerEl\" :class=\"[ns.b(), ns.e(layout)]\">\n    <slot />\n    <!-- Prevent iframe touch events from breaking -->\n    <div v-if=\"movingIndex\" :class=\"[ns.e('mask'), ns.e(`mask-${layout}`)]\" />\n  </div>\n</template>\n"], "names": ["useNamespace", "useContainer", "toRef", "useOrderedChildren", "getCurrentInstance", "watch", "useSize", "useResize", "provide", "splitterRootContextKey", "reactive", "_openBlock", "_normalizeClass", "_unref", "_renderSlot"], "mappings": ";;;;;;;;;;;;;;uCASc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAJA,IAAM,MAAA,EAAA,GAAKA,mBAAa,UAAU,CAAA,CAAA;AAclC,IAAM,MAAA,EAAE,aAAa,aAAc,EAAA,GAAIC,0BAAaC,SAAM,CAAA,KAAA,EAAO,QAAQ,CAAC,CAAA,CAAA;AAE1E,IAAM,MAAA;AAAA,MACJ,WAAa,EAAA,eAAA;AAAA,MACb,QAAU,EAAA,MAAA;AAAA,MACV,QAAU,EAAA,SAAA;AAAA,KACR,GAAAC,0BAAA,CAAmCC,sBAAmB,EAAA,EAAI,iBAAiB,CAAA,CAAA;AAE/E,IAAAC,SAAA,CAAM,QAAQ,MAAM;AAClB,MAAA,MAAA,CAAO,KAAM,CAAA,OAAA,CAAQ,CAAC,QAAA,EAA0B,KAAkB,KAAA;AAChE,QAAA,QAAA,CAAS,SAAS,KAAK,CAAA,CAAA;AAAA,OACxB,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAED,IAAA,MAAM,EAAE,YAAc,EAAA,OAAA,EAAY,GAAAC,eAAA,CAAQ,QAAQ,aAAa,CAAA,CAAA;AAE/D,IAAA,MAAM,EAAE,WAAa,EAAA,QAAA,EAAU,SAAW,EAAA,UAAA,EAAY,aAAgB,GAAAC,mBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,OAAA,CAAA,CAAA;AAAA,IACpE,MAAA,aAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACA,WAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACA,KAAA,CAAA,aAAA,EAAA,KAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,QAAA,GAAA,CAAA,KAAiB,EAAkB,MAAA,KAAA;AACvC,MAAA,QAAA,CAAA,KAAiB,EAAA,MAAA,CAAA,CAAA;AACjB,MAAM,KAAA,CAAA,QAAA,EAAA,KAAe,EAAO,OAAA,CAAA,KAAQ,CAAK,CAAA;AAAA,KAC3C,CAAA;AAEA,IAAM,MAAA,WAAW,GAAC,CAAA,KAAkC,KAAA;AAClD,MAAA,SAAS;AACT,MAAM,KAAA,CAAA,WAAiB,EAAA,KAAA,EAAA,OAAa,CAAA,KAAA,CAAA,CAAA;AAAA,KACtC,CAAA;AAEA,IAAMC,WAAA,CAAAC,2BAAiC,EAAAC,YAAA,CAAA;AACrC,MAAU,MAAA;AACV,MAAM,YAAA;AAAiC,MACzC,OAAA;AAEA,MAAA,MAAA,EAAA,KAAA,CAAA,MAAA;AAAA,MACE,WAAA;AAAA,MACA,aAAS;AAAA,MACP,WAAA,EAAA,aAAA;AAAA,MACA,QAAA,EAAA,QAAA;AAAA,MACA,SAAA,EAAA,WAAA;AAAA,MAAA,UACQ;AAAM,MACd,aAAA,EAAA,CAAA,KAAA,KAAA;AAAA,QACA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OAAA;AACa,MAAA,SACH;AAAA,MAAA,eACC;AAAA,KACX,CAAA,CAAA,CAAA;AAAA,IACA,OAAA,CAAA,IAAA,EAAA,MAAgB,KAA0B;AACxC,MAAO,OAAAC,aAAM,0BAAU,CAAA,KAAA,EAAA;AAAA,QACzB,OAAA,EAAA,aAAA;AAAA,QACA,GAAA,EAAA,WAAA;AAAA,QACA,KAAA,EAAAC,kBAAA,CAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAAA,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,OACD,EAAA;AAAA,QACHC,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;;;;;;;;;;;;;;"}