# unc-path-regex [![NPM version](https://badge.fury.io/js/unc-path-regex.svg)](http://badge.fury.io/js/unc-path-regex)  [![Build Status](https://travis-ci.org/jonschlinkert/unc-path-regex.svg)](https://travis-ci.org/jonschlinkert/unc-path-regex)

> Regular expression for testing if a file path is a windows UNC file path. Can also be used as a component of another regexp via the `.source` property.

Visit the MSDN reference for [Common Data Types 2.2.57 UNC](https://msdn.microsoft.com/en-us/library/gg465305.aspx) for more information about UNC paths.

## Install

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i unc-path-regex --save
```

## Usage

```js
// unc-path-regex returns a function
var regex = require('unc-path-regex')();
```

**true**

Returns true for windows UNC paths:

```js
regex.test('\\/foo/bar');
regex.test('\\\\foo/bar');
regex.test('\\\\foo\\admin$');
regex.test('\\\\foo\\admin$\\system32');
regex.test('\\\\foo\\temp');
regex.test('\\\\/foo/bar');
regex.test('\\\\\\/foo/bar');
```

**false**

Returns false for non-UNC paths:

```js
regex.test('/foo/bar');
regex.test('/');
regex.test('/foo');
regex.test('/foo/');
regex.test('c:');
regex.test('c:.');
regex.test('c:./');
regex.test('c:./file');
regex.test('c:/');
regex.test('c:/file');
```

## Related projects

* [dotfile-regex](https://github.com/regexps/dotfile-regex): Regular expresson for matching dotfiles.
* [dotdir-regex](https://github.com/regexps/dotdir-regex): Regex for matching dot-directories, like `.git/`
* [dirname-regex](https://github.com/regexps/dirname-regex): Regular expression for matching the directory part of a file path.
* [is-unc-path](https://github.com/jonschlinkert/is-unc-path): Returns true if a filepath is a windows UNC file path.
* [is-glob](https://github.com/jonschlinkert/is-glob): Returns `true` if the given string looks like a glob pattern.
* [path-regex](https://github.com/regexps/path-regex): Regular expression for matching the parts of a file path.

## Running tests

Install dev dependencies:

```sh
$ npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/unc-path-regex/issues/new)

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2015 Jon Schlinkert
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on July 07, 2015._