"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  NeuePlusResolver: () => NeuePlusResolver
});
module.exports = __toCommonJS(index_exports);
function NeuePlusResolver(options = {}) {
  const { importStyle = "sass", prefix = "Ne" } = options;
  return {
    type: "component",
    resolve: (name) => {
            console.log(prefix,name, "kebabCaseName123");
      if (!name.startsWith(prefix)) return;
      const partialName = name.slice(prefix.length);
      const kebabCaseName = partialName.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
      const path = `@neue-plus/components/${kebabCaseName}`;
      const stylePath = `@neue-plus/theme-chalk/src/${kebabCaseName}.${importStyle === "sass" ? "scss" : "css"}`;
           console.log(path, "kebabCaseName");
      return {
        name,
        from: path,
        sideEffects: importStyle ? [stylePath] : void 0
      };
    }
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  NeuePlusResolver
});
