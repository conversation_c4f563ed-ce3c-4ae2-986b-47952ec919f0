{"version": 3, "file": "Coverage.js", "sourceRoot": "", "sources": ["../../../../src/common/Coverage.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAEH,iDAAyC;AACzC,uCAA+E;AAI/E,+DAA4D;AAC5D,uCAA+C;AAmE/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACH,MAAa,QAAQ;IAInB,YAAY,MAAkB;QAH9B,uCAAwB;QACxB,wCAA0B;QAGxB,uBAAA,IAAI,wBAAe,IAAI,UAAU,CAAC,MAAM,CAAC,MAAA,CAAC;QAC1C,uBAAA,IAAI,yBAAgB,IAAI,WAAW,CAAC,MAAM,CAAC,MAAA,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,eAAe,CAAC,UAA6B,EAAE;QACnD,OAAO,MAAM,uBAAA,IAAI,4BAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,cAAc;QAClB,OAAO,MAAM,uBAAA,IAAI,4BAAY,CAAC,IAAI,EAAE,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,gBAAgB,CAAC,UAA8B,EAAE;QACrD,OAAO,MAAM,uBAAA,IAAI,6BAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,eAAe;QACnB,OAAO,MAAM,uBAAA,IAAI,6BAAa,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;CACF;AAxDD,4BAwDC;;AAED;;GAEG;AACH,MAAa,UAAU;IAUrB,YAAY,MAAkB;;QAT9B,qCAAoB;QACpB,8BAAW,KAAK,EAAC;QACjB,iCAAc,IAAI,GAAG,EAAkB,EAAC;QACxC,oCAAiB,IAAI,GAAG,EAAkB,EAAC;QAC3C,qCAA4C,EAAE,EAAC;QAC/C,wCAAqB,KAAK,EAAC;QAC3B,6CAA0B,KAAK,EAAC;QAChC,+CAA4B,KAAK,EAAC;QAGhC,uBAAA,IAAI,sBAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,KAAK,CACT,UAII,EAAE;QAEN,IAAA,kBAAM,EAAC,CAAC,uBAAA,IAAI,2BAAS,EAAE,+BAA+B,CAAC,CAAC;QACxD,MAAM,EACJ,iBAAiB,GAAG,IAAI,EACxB,sBAAsB,GAAG,KAAK,EAC9B,wBAAwB,GAAG,KAAK,GACjC,GAAG,OAAO,CAAC;QACZ,uBAAA,IAAI,iCAAsB,iBAAiB,MAAA,CAAC;QAC5C,uBAAA,IAAI,sCAA2B,sBAAsB,MAAA,CAAC;QACtD,uBAAA,IAAI,wCAA6B,wBAAwB,MAAA,CAAC;QAC1D,uBAAA,IAAI,uBAAY,IAAI,MAAA,CAAC;QACrB,uBAAA,IAAI,8BAAY,CAAC,KAAK,EAAE,CAAC;QACzB,uBAAA,IAAI,iCAAe,CAAC,KAAK,EAAE,CAAC;QAC5B,uBAAA,IAAI,8BAAmB;YACrB,IAAA,0BAAgB,EACd,uBAAA,IAAI,0BAAQ,EACZ,uBAAuB,EACvB,uBAAA,IAAI,yDAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,0BAAQ,EACZ,kCAAkC,EAClC,uBAAA,IAAI,qEAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5C;SACF,MAAA,CAAC;QACF,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACpC,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBACjD,SAAS,EAAE,uBAAA,IAAI,4CAA0B;gBACzC,QAAQ,EAAE,IAAI;aACf,CAAC;YACF,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACpC,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC;SAC7D,CAAC,CAAC;IACL,CAAC;IAiCD,KAAK,CAAC,IAAI;QACR,IAAA,kBAAM,EAAC,uBAAA,IAAI,2BAAS,EAAE,2BAA2B,CAAC,CAAC;QACnD,uBAAA,IAAI,uBAAY,KAAK,MAAA,CAAC;QAEtB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC/B,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC;YACjD,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC;YACjD,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACrC,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;SACtC,CAAC,CAAC;QAEH,IAAA,8BAAoB,EAAC,uBAAA,IAAI,kCAAgB,CAAC,CAAC;QAE3C,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAElC,KAAK,MAAM,KAAK,IAAI,eAAe,CAAC,MAAM,EAAE;YAC1C,IAAI,GAAG,GAAG,uBAAA,IAAI,8BAAY,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,GAAG,IAAI,uBAAA,IAAI,0CAAwB,EAAE;gBACxC,GAAG,GAAG,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAC;aACxC;YACD,MAAM,IAAI,GAAG,uBAAA,IAAI,iCAAe,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAI,IAAI,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,EAAE;gBAC3C,SAAS;aACV;YACD,MAAM,aAAa,GAAG,EAAE,CAAC;YACzB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,SAAS,EAAE;gBAClC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;aACpC;YACD,MAAM,MAAM,GAAG,uBAAuB,CAAC,aAAa,CAAC,CAAC;YACtD,IAAI,CAAC,uBAAA,IAAI,4CAA0B,EAAE;gBACnC,QAAQ,CAAC,IAAI,CAAC,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;aACpC;iBAAM;gBACL,QAAQ,CAAC,IAAI,CAAC,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAC,CAAC,CAAC;aAC9D;SACF;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA7HD,gCA6HC;;IApEG,IAAI,CAAC,uBAAA,IAAI,qCAAmB,EAAE;QAC5B,OAAO;KACR;IACD,uBAAA,IAAI,8BAAY,CAAC,KAAK,EAAE,CAAC;IACzB,uBAAA,IAAI,iCAAe,CAAC,KAAK,EAAE,CAAC;AAC9B,CAAC,+BAED,KAAK,qCACH,KAA0C;IAE1C,oCAAoC;IACpC,IAAI,KAAK,CAAC,GAAG,KAAK,2CAAqB,EAAE;QACvC,OAAO;KACR;IACD,mFAAmF;IACnF,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,uBAAA,IAAI,0CAAwB,EAAE;QAC/C,OAAO;KACR;IACD,IAAI;QACF,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,0BAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACnE,QAAQ,EAAE,KAAK,CAAC,QAAQ;SACzB,CAAC,CAAC;QACH,uBAAA,IAAI,8BAAY,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QAChD,uBAAA,IAAI,iCAAe,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;KAChE;IAAC,OAAO,KAAK,EAAE;QACd,4DAA4D;QAC5D,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;KACnB;AACH,CAAC;AA0CH;;GAEG;AACH,MAAa,WAAW;IAQtB,YAAY,MAAkB;;QAP9B,sCAAoB;QACpB,+BAAW,KAAK,EAAC;QACjB,sCAAkB,IAAI,GAAG,EAAkB,EAAC;QAC5C,yCAAqB,IAAI,GAAG,EAAkB,EAAC;QAC/C,sCAA4C,EAAE,EAAC;QAC/C,yCAAqB,KAAK,EAAC;QAGzB,uBAAA,IAAI,uBAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,UAAyC,EAAE;QACrD,IAAA,kBAAM,EAAC,CAAC,uBAAA,IAAI,4BAAS,EAAE,gCAAgC,CAAC,CAAC;QACzD,MAAM,EAAC,iBAAiB,GAAG,IAAI,EAAC,GAAG,OAAO,CAAC;QAC3C,uBAAA,IAAI,kCAAsB,iBAAiB,MAAA,CAAC;QAC5C,uBAAA,IAAI,wBAAY,IAAI,MAAA,CAAC;QACrB,uBAAA,IAAI,mCAAgB,CAAC,KAAK,EAAE,CAAC;QAC7B,uBAAA,IAAI,sCAAmB,CAAC,KAAK,EAAE,CAAC;QAChC,uBAAA,IAAI,+BAAmB;YACrB,IAAA,0BAAgB,EACd,uBAAA,IAAI,2BAAQ,EACZ,qBAAqB,EACrB,uBAAA,IAAI,yDAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9B;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,2BAAQ,EACZ,kCAAkC,EAClC,uBAAA,IAAI,uEAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5C;SACF,MAAA,CAAC;QACF,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;YAC/B,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;YAC/B,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAAC,4BAA4B,CAAC;SAChD,CAAC,CAAC;IACL,CAAC;IA4BD,KAAK,CAAC,IAAI;QACR,IAAA,kBAAM,EAAC,uBAAA,IAAI,4BAAS,EAAE,4BAA4B,CAAC,CAAC;QACpD,uBAAA,IAAI,wBAAY,KAAK,MAAA,CAAC;QACtB,MAAM,oBAAoB,GAAG,MAAM,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAClD,2BAA2B,CAC5B,CAAC;QACF,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;YAChC,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;SACjC,CAAC,CAAC;QACH,IAAA,8BAAoB,EAAC,uBAAA,IAAI,mCAAgB,CAAC,CAAC;QAE3C,4BAA4B;QAC5B,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;QACzC,KAAK,MAAM,KAAK,IAAI,oBAAoB,CAAC,SAAS,EAAE;YAClD,IAAI,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC5D,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,GAAG,EAAE,CAAC;gBACZ,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;aACxD;YACD,MAAM,CAAC,IAAI,CAAC;gBACV,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC1B,CAAC,CAAC;SACJ;QAED,MAAM,QAAQ,GAAoB,EAAE,CAAC;QACrC,KAAK,MAAM,YAAY,IAAI,uBAAA,IAAI,mCAAgB,CAAC,IAAI,EAAE,EAAE;YACtD,MAAM,GAAG,GAAG,uBAAA,IAAI,mCAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACnD,IAAA,kBAAM,EACJ,OAAO,GAAG,KAAK,WAAW,EAC1B,6CAA6C,YAAY,GAAG,CAC7D,CAAC;YACF,MAAM,IAAI,GAAG,uBAAA,IAAI,sCAAmB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACvD,IAAA,kBAAM,EACJ,OAAO,IAAI,KAAK,WAAW,EAC3B,8CAA8C,YAAY,GAAG,CAC9D,CAAC;YACF,MAAM,MAAM,GAAG,uBAAuB,CACpC,sBAAsB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAC/C,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;SACpC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA/GD,kCA+GC;;IAxEG,IAAI,CAAC,uBAAA,IAAI,sCAAmB,EAAE;QAC5B,OAAO;KACR;IACD,uBAAA,IAAI,mCAAgB,CAAC,KAAK,EAAE,CAAC;IAC7B,uBAAA,IAAI,sCAAmB,CAAC,KAAK,EAAE,CAAC;AAClC,CAAC,8BAED,KAAK,oCAAe,KAAwC;IAC1D,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5B,2BAA2B;IAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;QACrB,OAAO;KACR;IACD,IAAI;QACF,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,2BAAQ,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAChE,YAAY,EAAE,MAAM,CAAC,YAAY;SAClC,CAAC,CAAC;QACH,uBAAA,IAAI,mCAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAChE,uBAAA,IAAI,sCAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;KACjE;IAAC,OAAO,KAAK,EAAE;QACd,4DAA4D;QAC5D,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;KACnB;AACH,CAAC;AAmDH,SAAS,uBAAuB,CAC9B,YAA4E;IAE5E,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE;QAChC,MAAM,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAC,CAAC,CAAC;QACzD,MAAM,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAC,CAAC,CAAC;KACxD;IACD,oDAAoD;IACpD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACnB,gCAAgC;QAChC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;YACzB,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;SAC5B;QACD,oDAAoD;QACpD,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE;YACrB,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;SACxB;QACD,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;QACxD,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;QACxD,gEAAgE;QAChE,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;YAChB,OAAO,OAAO,GAAG,OAAO,CAAC;SAC1B;QACD,+DAA+D;QAC/D,OAAO,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,EAAE,CAAC;IACzB,MAAM,OAAO,GAGR,EAAE,CAAC;IACR,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,6CAA6C;IAC7C,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,IACE,aAAa,CAAC,MAAM;YACpB,UAAU,GAAG,KAAK,CAAC,MAAM;YACzB,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAE,GAAG,CAAC,EAC5C;YACA,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC/C,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,KAAK,UAAU,EAAE;gBAC/C,UAAU,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;aAC/B;iBAAM;gBACL,OAAO,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM,EAAC,CAAC,CAAC;aACtD;SACF;QACD,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE;YACpB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACvC;aAAM;YACL,aAAa,CAAC,GAAG,EAAE,CAAC;SACrB;KACF;IACD,2BAA2B;IAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QAC5B,OAAO,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC"}